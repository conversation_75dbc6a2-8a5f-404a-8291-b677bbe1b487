{"name": "ryvyl-merchant-profile", "version": "1.0.0", "main": "index.js", "_moduleAliases": {"@submodules": "./submodules"}, "scripts": {"preinstall": "node checkNodeVersion", "prestart": "node checkNodeVersion", "start": "ts-node-dev --respawn --transpile-only --exit-child --clear ./src/index.ts", "build": "tsc --sourcemap", "prod": "node ./dist/src/index.js", "format": "pretty-quick --staged", "prepare": "husky", "test": "jest --verbose --setupFiles dotenv/config --testPathPattern=\"src/\"", "test:watch": "jest --verbose --watch --testPathPattern=\"src/\"", "test:coverage": "jest --coverage --testPathPattern=\"src/\"", "update-ryvyl-commons": "git submodule update --init --recursive --remote submodules/ryvyl-commons", "replace-paths": "node submodules/ryvyl-commons/scripts/replace-paths.js ./src/"}, "keywords": [], "author": "Encorp.io", "license": "ISC", "description": "API for Ryvyl merchant screening and compliance checks using Mastercard Match Pro and Visa VMSS", "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.9.0", "@types/winston-loggly-bulk": "^3.0.6", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "eslint": "^9.14.0", "eslint-config-google": "^0.14.0", "eslint-plugin-jsdoc": "^50.5.0", "husky": "^9.1.6", "jest": "^29.7.0", "lint-staged": "^15.2.10", "nodemon": "^3.1.7", "pretty-quick": "^4.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.6.3"}, "dependencies": {"@types/puppeteer": "^7.0.4", "common": "file:submodules/ryvyl-commons", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "express-validator": "^7.2.0", "handlebars": "^4.7.8", "helmet": "^8.0.0", "http": "^0.0.1-security", "i18n-iso-countries": "^7.13.0", "joi": "^17.13.3", "libphonenumber-js": "^1.12.6", "module-alias": "^2.2.3", "mongoose": "^8.8.1", "mongoose-paginate-v2": "^1.9.0", "puppeteer": "23.10.2", "winston": "^3.17.0", "winston-loggly-bulk": "^3.3.2"}}