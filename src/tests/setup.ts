// Set required environment variables before any tests run
process.env.ENV = 'test';
process.env.LOG_LEVEL = 'debug';
process.env.LOGGER_UPLOAD_LOGS = 'false';

// Add all environment variables needed for testing
// Base URLs for external services
process.env.SUMSUB_RYVYL_SERVICE_BASE_URL = 'http://mock-sumsub-url';
process.env.RYVYL_MASTERCARD_MATCH_PRO_SERVICE_BASE_URL = 'http://mock-mastercard-url';
process.env.RYVYL_VISA_VMSS_SERVICE_BASE_URL = 'http://mock-visa-url';
process.env.MCC_SERVICE_CLASSIFIER_BASE_URL = 'http://mock-mcc-classifier-url';

// SumSub configuration
process.env.SUMSUB_ACQUIRING_ENTITY_LEVEL_NAME = 'test_level';
process.env.SUMSUB_ACQUIRING_ENTITY_URL_LEVEL_NAME = 'mock-level';
process.env.SUMSUB_ACQUIRING_ENTITY_BENEFICIARY_LEVEL_NAME = 'mock-beneficiary-level';
