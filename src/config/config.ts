import { getEnvOrRevert } from '@submodules/ryvyl-commons/config';

export const IS_PRODUCTION = getEnvOrRevert('ENV') === 'PROD';

export const DB = {
  HOST: process.env.MONGODB_HOST || 'localhost',
  PORT: process.env.MONGODB_PORT || '27017',
  NAME: process.env.MONGODB_DATABASE_NAME || '',
  USERNAME: process.env.MONGODB_USERNAME || '',
  PASSWORD: process.env.MONGODB_PASSWORD || '',
  URI: process.env.MONGODB_URI || '',
  CA_CERT: process.env.CA_CERT
};

export const SUMSUB_RYVYL_SERVICE_BASE_URL = getEnvOrRevert('SUMSUB_RYVYL_SERVICE_BASE_URL');

export const SUMSUB = {
  ACQUIRING_ENTITY_LEVEL_NAME: getEnvOrRevert('SUMSUB_ACQUIRING_ENTITY_LEVEL_NAME'),
  ACQUIRING_ENTITY_URL_LEVEL_NAME: getEnvOrRevert('SUMSUB_ACQUIRING_ENTITY_URL_LEVEL_NAME'),
  ACQUIRING_ENTITY_BENEFICIARY_LEVEL_NAME: getEnvOrRevert('SUMSUB_ACQUIRING_ENTITY_BENEFICIARY_LEVEL_NAME')
};

export const MCC_SERVICE_CLASSIFIER_BASE_URL = getEnvOrRevert('MCC_SERVICE_CLASSIFIER_BASE_URL');
