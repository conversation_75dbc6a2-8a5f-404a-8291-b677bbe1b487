import { Request, Response } from 'express';

import handleCustomError from '../utils/handleCustomErrorResponse';
import { getAcquiringEntityMatchInVisaVmssDashboard } from '../services/visaVmssService';
import { checkAcquiringEntityMatchInVmss } from '../services/acquiringEntityService';

export const checkAcquiringEntityMatchInVmssController = async (req: Request, res: Response) => {
  const acquiringEntityId = req.params.acquiringEntityId;

  try {
    const response = await checkAcquiringEntityMatchInVmss(acquiringEntityId);

    res.status(200).json({ data: { message: 'Acquiring entity match in Visa VMSS successfully' } });
  } catch (error) {
    handleCustomError(error, res);
  }
};

export const getVisaVmssMatchDashboardController = async (req: Request, res: Response) => {
  const acquiringEntityId = req.params.acquiringEntityId;
  const page_length = Number(req.query.limit) || 1000;
  let page_offset = Number(req.query.page) || 0;

  if (!page_offset) {
    page_offset = 0;
  } else {
    // This is because the dashboard page is starting from 1 but in mastercard it starts from 0
    page_offset = page_offset - 1;
  }

  try {
    const response = await getAcquiringEntityMatchInVisaVmssDashboard(acquiringEntityId, page_length, page_offset);
    res.status(200).json({ data: response });
  } catch (error) {
    handleCustomError(error, res);
  }
};

export const checkVisaVmssController = async (req: Request, res: Response) => {
  try {
    const { acquiringEntityId } = req.body;
    if (!acquiringEntityId) {
      res.status(400).json({ data: { message: 'Acquiring entity id is required' } });
      return;
    }
    const response = await checkAcquiringEntityMatchInVmss(acquiringEntityId);
    res.status(200).json({ data: { message: 'Acquiring entity match in Visa VMSS successfully' } });
  } catch (error) {
    handleCustomError(error, res);
  }
};
