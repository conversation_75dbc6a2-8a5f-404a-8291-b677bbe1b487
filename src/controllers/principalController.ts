import { Request, Response } from 'express';
import { getPrincipalDefaultValues } from '../services/principalService';
import handleCustomError from '../utils/handleCustomErrorResponse';

export const getPrincipalDefaultValuesController = async (req: Request, res: Response) => {
  try {
    const principalDefaultValues = getPrincipalDefaultValues();
    res.status(200).json({ data: principalDefaultValues });
  } catch (error) {
    handleCustomError(error, res);
  }
};
