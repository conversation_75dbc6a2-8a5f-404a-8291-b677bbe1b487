import { Request, Response } from 'express';

import handleCustomError from '../utils/handleCustomErrorResponse';
import {
  getRetrievedHistoryTermination,
  getAcquiringEntityMatchInMastercardDashboard
} from '../services/mastercardService';
import {
  checkAcquiringEntityMatchInMastercard,
  getAllMatchReferenceForMastercardDashboard
} from '../services/acquiringEntityService';

export const checkMastercardMatchProController = async (req: Request, res: Response) => {
  try {
    const { acquiringEntityId } = req.body;
    if (!acquiringEntityId) {
      res.status(400).json({ data: { message: 'Acquiring entity id is required' } });
      return;
    }
    const response = await checkAcquiringEntityMatchInMastercard(acquiringEntityId);
    res.status(200).json({ data: { message: 'Acquiring entity match in Mastercard successfully' } });
  } catch (error) {
    handleCustomError(error, res);
  }
};

export const getTerminationHistoryByReferenceController = async (req: Request, res: Response) => {
  const referenceNumber = req.params.referenceNumber;
  const page_length = Number(req.query.limit) || 10;
  let page_offset = Number(req.query.page) || 0;

  if (!page_offset) {
    page_offset = 0;
  } else {
    // This is because the dashboard page is starting from 1 but in mastercard it starts from 0
    page_offset = page_offset - 1;
  }

  try {
    const response = await getRetrievedHistoryTermination(referenceNumber, page_length, page_offset);
    res.status(200).json({ data: response });
  } catch (error) {
    handleCustomError(error, res);
  }
};

// Get the matched entities for a reference number
export const mastercardMatchProDashboardController = async (req: Request, res: Response) => {
  const acquiringEntityId = req.params.acquiringEntityId;
  const page_length = Number(req.query.limit) || 10;
  let page_offset = Number(req.query.page) || 0;
  const mastercardRefNumberMatched = req.query.mastercardRefNumberMatched;

  if (!mastercardRefNumberMatched) {
    res.status(400).json({ data: { message: 'Mastercard ref number matched in query is required' } });
    return;
  }

  if (!page_offset) {
    page_offset = 0;
  } else {
    // This is because the dashboard page is starting from 1 but in mastercard it starts from 0
    page_offset = page_offset - 1;
  }

  try {
    const response = await getAcquiringEntityMatchInMastercardDashboard(
      acquiringEntityId,
      page_length,
      page_offset,
      mastercardRefNumberMatched as string
    );
    res.status(200).json({ data: response });
  } catch (error) {
    handleCustomError(error, res);
  }
};

export const mastercardMatchProAllMatchReferenceDashboardController = async (req: Request, res: Response) => {
  const acquiringEntityId = req.params.acquiringEntityId;
  const page_length = 1000;
  let page_offset = 0;

  try {
    const response = await getAllMatchReferenceForMastercardDashboard(acquiringEntityId, page_length, page_offset);
    res.status(200).json({ data: response });
  } catch (error) {
    handleCustomError(error, res);
  }
};
