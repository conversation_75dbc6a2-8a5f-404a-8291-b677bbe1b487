import { Request, Response } from 'express';
import {
  acquiringEntityUpdate,
  createAcquiringEntityWithPrincipals,
  getAcquiringEntitiesWithFilters,
  getAcquiringEntityById,
  getAcquiringEntityWithAllProperties,
  manualApproveAcquireMastercard,
  manualApproveAcquireMastercardByRefNumber,
  manualApproveAcquireVisaVmss
} from '../services/acquiringEntityService';
import { schemaAcquiringEntityValidation } from '../schemaValidations/acquiringEntitySchemaValidation';
import AcquiringEntityModel, { AcquiringEntityModelDocument } from '../models/mongo/acquiringEntity';
import { AcquiringEntity, AcquiringEntityQuery } from '../interfaces/acquiringEntityInterface';
import { PaginateOptions } from 'mongoose';
import logger, { acquiringEntityLogger } from '../utils/logger';
import { CustomError } from '../classes/CustomError';
import handleCustomError from '../utils/handleCustomErrorResponse';
import { transformDataForGetAllAcquiringEntities } from '../services/transformDataForDashboardService';
import { CustomRequestWithUser } from '@submodules/ryvyl-commons/types/express';
import { configureMidReference } from '../services/midService';
import { clearMidConfiguration } from '../services/acquiringEntityService';
import { publishAciEvent, publishOnboardingEvent } from '../kafka/publishers/onboarding';

export async function createAcquiringEntityController(req: Request, res: Response) {
  const { error } = schemaAcquiringEntityValidation.validate(req.body, { abortEarly: false });

  if (error) {
    const errorMessages = error.details.map((err) => err.message.replace(/"/g, '')).join(',\n');
    res.status(400).json({ message: `Validation errors:\n${errorMessages}` });
    return;
  }
  const body: AcquiringEntity = req.body;
  try {
    const result = await createAcquiringEntityWithPrincipals(body);
    res.status(200).json({ data: result });
    return;
  } catch (error: any) {
    acquiringEntityLogger.error(
      `Error in createAcquiringEntityController with registration number: ${body.registrationNumber} error: ${error.message}`
    );
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({ message: error.message });
      return;
    }
    res.status(500).json({ message: 'Something went wrong, please try again!' });
    return;
  }
}

export async function createAcquiringEntityDashboardController(req: CustomRequestWithUser, res: Response) {
  const { error } = schemaAcquiringEntityValidation.validate(req.body, { abortEarly: false });

  if (error) {
    const errorMessages = error.details.map((err) => err.message.replace(/"/g, '')).join(',\n');
    res.status(400).json({ message: `Validation errors:\n${errorMessages}` });
    return;
  }
  const body: AcquiringEntity = req.body;
  try {
    const result = await createAcquiringEntityWithPrincipals(body);
    res.status(200).json({ data: result });
    return;
  } catch (error: any) {
    acquiringEntityLogger.error(
      `Error in createAcquiringEntityDashboardController with registration number: ${body.registrationNumber} error: ${error.message}`
    );
    if (error instanceof CustomError) {
      res.status(error.statusCode).json({ message: error.message });
      return;
    }
    res.status(500).json({ message: 'Something went wrong, please try again!' });
    return;
  }
}

export async function getAcquiringEntities(req: Request, res: Response) {
  const pageParam = (req.query.page as string) ?? 1;
  const limitParam = (req.query.limit as string) ?? 10;
  const page = parseInt(pageParam);
  const limit = parseInt(limitParam);
  const sortBy = (req.query.sortBy as string) || 'updatedAt'; // default sorting field
  const sortOrder = req.query.sortOrder === 'asc' ? 1 : -1; // sorting order: ascending (asc) or descending (desc)

  // List of valid sortable fields from the schema
  let validSortFields = Object.keys(AcquiringEntityModel.schema.obj);
  validSortFields = [...validSortFields, 'createdAt', 'updatedAt'];

  // Check if the provided sortBy field is valid
  const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';

  try {
    // Extract search and filter parameters
    const name = req.query.name as string;
    const registrationNumber = req.query.registrationNumber as string;

    // Get match status parameters
    const visaMatchStatus = req.query.visaMatchStatuses as string[];
    const mastercardMatchStatus = req.query.mastercardMatchStatuses as string[];
    const sumsubAnswer = req.query.sumsubAnswers as string[];
    const sumsubStatuses = req.query.sumsubStatuses as string[];

    // Build query object
    const queryParams: AcquiringEntityQuery = {
      name,
      registrationNumber,
      visaMatchStatus,
      mastercardMatchStatus,
      sumsubAnswer,
      sumsubStatuses
    };

    // Remove undefined values from query
    Object.keys(queryParams).forEach((key) => {
      if (queryParams[key as keyof AcquiringEntityQuery] === undefined) {
        delete queryParams[key as keyof AcquiringEntityQuery];
      }
    });

    const options: PaginateOptions = {
      page,
      limit,
      populate: [
        { path: 'principalsIds', model: 'principal' } // Populating referenced Principals
      ],
      select: `
       -principalRawData
       `,
      sort: { [sortField]: sortOrder },
      // Add collation for case-insensitive sorting
      collation: {
        locale: 'en',
        strength: 2 // Case-insensitive comparison
      }
    };

    const result: any = await getAcquiringEntitiesWithFilters(queryParams, options);
    const transformedResult = await transformDataForGetAllAcquiringEntities(result.docs);
    result.docs = transformedResult;
    res.json({ data: result });
  } catch (error: any) {
    logger.error(`Error fetching acquiring entities: ${error.message}`);
    res.status(500).json({ message: 'An error occurred while fetching acquiring entities' });
  }
}

export const mastercardMatchProHandleManualAcquiringEntityController = async (req: Request, res: Response) => {
  const body = req.body;
  body.typeOfAction = body.typeOfAction.toLowerCase();
  try {
    await manualApproveAcquireMastercard(body);
    res.status(200).json({ data: { message: 'Successfully updated.' } });
  } catch (error) {
    handleCustomError(error, res);
  }
};
export const mastercardMatchProHandleManualAcquiringEntityByRefNumberController = async (
  req: Request,
  res: Response
) => {
  const body = req.body;
  body.typeOfAction = body.typeOfAction.toLowerCase();
  try {
    await manualApproveAcquireMastercardByRefNumber(body);
    res.status(200).json({ data: { message: 'Successfully updated.' } });
  } catch (error) {
    handleCustomError(error, res);
  }
};

export const visaVmssHandleManualAcquiringEntityController = async (req: Request, res: Response) => {
  const body = req.body;
  body.typeOfAction = body.typeOfAction.toLowerCase();

  try {
    await manualApproveAcquireVisaVmss(body);
    res.status(200).json({ data: { message: 'Successfully updated.' } });
  } catch (error) {
    handleCustomError(error, res);
  }
};

export const getAcquiringEntityWithAllPropertiesController = async (req: Request, res: Response) => {
  const { acquiringEntityId } = req.params;

  try {
    const acquiringEntity = await getAcquiringEntityWithAllProperties(acquiringEntityId);
    res.status(200).json({ data: acquiringEntity });
  } catch (error) {
    handleCustomError(error, res);
  }
};

export const postAcquiringEntityUpdateController = async (req: CustomRequestWithUser, res: Response) => {
  const body = req.body;
  const user = req.user;
  try {
    const result = await acquiringEntityUpdate(body, user.username);
    res.status(200).json({ data: result });
  } catch (error: any) {
    acquiringEntityLogger.error(`Error in postAcquiringEntityUpdateController: ${error.message}`);
    handleCustomError(error, res);
  }
};

export const processMidConfigurationController = async (req: Request, res: Response) => {
  const { acquiringEntity, parentMerchantId, pspNumber } = req.body;
  const entrypoint = req.params.entrypoint;
  try {
    switch (entrypoint) {
      case 'send': {
        await configureMidReference(acquiringEntity as AcquiringEntityModelDocument, parentMerchantId, pspNumber);
        res.status(200).json({ data: { message: 'MID configuration sent for processing!' } });
        break;
      }
      case 'reset':
        await clearMidConfiguration(acquiringEntity._id);
        res.status(200).json({ data: { message: 'MID configuration cleared!' } });
        break;
      default:
        throw new CustomError('Invalid entrypoint', 400);
    }
  } catch (error) {
    handleCustomError(error, res);
  }
};

export const retryToOnboardController = async (req: Request, res: Response) => {
  const { entrypoint, acquiringEntityId } = req.params;

  if (!acquiringEntityId) {
    res.status(400).json({ data: { message: 'Acquiring entity id is required' } });
    return;
  }

  try {
    const acquiringEntity = await getAcquiringEntityById(acquiringEntityId as string);
    if (!acquiringEntity) {
      throw new CustomError('Acquiring entity not found', 404);
    }
    const message = {
      id: acquiringEntityId,
      result: acquiringEntity
    };
    switch (entrypoint) {
      case 'omnipay':
        await publishOnboardingEvent(`onboard-${acquiringEntity._id}`, message);
        res.status(200).json({ data: { message: 'Retry onboarding to OmniPay started!' } });
        break;
      case 'aci':
        await publishAciEvent(`aci-${acquiringEntity._id}`, message);
        res.status(200).json({ data: { message: 'Retry onboarding to ACI started!' } });
        break;
      default:
        throw new CustomError('Invalid entrypoint', 400);
    }
  } catch (error) {
    handleCustomError(error, res);
  }
};
