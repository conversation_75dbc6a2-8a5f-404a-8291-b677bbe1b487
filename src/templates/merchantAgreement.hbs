<html lang='en'>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Merchant Agreement</title>
    <style>
      @page { size: A4; margin: 60px 12px 20px 12px; }
      body {
        font-family: Helvetica, Arial, sans-serif;
        margin: 0;
        padding: 0px 20px 20px 20px;
        color: #000000;
        line-height: normal;
        font-size: 10pt;
        background-color: white;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        padding-left: 20px;
        padding-right: 20px;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 40px;
        position: relative;
      }
      .logo-container {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .logo {
        max-width: 250px;
        max-height: 100px;
      }
      .date-container {
        position: absolute;
        right: 0;
        top: 0;
      }
      .date {
        text-align: right;
        font-size: 10pt;
        color: #333;
      }
      .title {
        text-align: center;
        font-size: 18pt;
        font-weight: bold;
        margin-bottom: 12px;
        color: #222;
      }
      .subtitle {
        text-align: center;
        font-size: 14pt;
        margin-bottom: 40px;
        color: #444;
      }
      .section {
        margin-bottom: 20px;
      }
      .section-title {
        font-size: 14pt;
        font-weight: bold;
        margin-bottom: 13px;
        color: #222;
        text-decoration: underline;
      }
      .company-name {
        font-weight: bold;
        font-size: 13pt;
        margin: 10px 0;
      }
      .principal-name {
        font-weight: bold;
        font-size: 13pt;
        margin: 10px 0;
      }
      .bold-text {
        font-weight: bold;
      }
      .field-label {
        font-weight: bold;
        display: inline-block;
        min-width: 280px;
        max-width: 280px;
        white-space: nowrap;
        vertical-align: top;
        margin-right: 10px;
      }
      .website-section {
        margin-bottom: 20px;
      }
      .website-title {
        font-weight: bold;
        color: #333;
        margin-bottom: 12px;
        font-size: 12pt;
      }
      .field-row {
        margin-bottom: 6px;
        display: flex;
        flex-wrap: nowrap;
        align-items: baseline;
      }
      .field-row span {
        word-break: normal;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .page-break {
        page-break-after: always;
      }
      p {
        margin: 4px 0;
        font-size: 13px;
      }
      .address {
        padding-left: 20px;
        margin-bottom: 12px;
      }
      .residing-at {
        margin: 0;
      }
    </style>
  </head>
  <body>
    <div class='container'>
      <!-- Header with logo and date -->
      <div class='header'>
        <div class='logo-container'>
          <img src='{{logoSrc}}' alt='Ryvyl Logo' class='logo' />
        </div>
        <div class='date-container'>
          <div class='date'>
            {{date}}
          </div>
        </div>
      </div>

      <!-- Title and subtitle -->
      <div class='title'>Merchant Agreement</div>
      <div class='subtitle'>Acquiring Services</div>

      <!-- Introduction -->
      <div class='section'>
        <p class='bold-text'>This Merchant Agreement has been submitted on behalf of</p>
        <p class='company-name'>{{#if name}}{{name}}{{else}}[Company Name]{{/if}},</p>
        <p>(the "Company"), a company incorporated on
          {{#if incorporationDate}}{{incorporationDate}}{{else}}[Incorporation Date]{{/if}}
          under Incorporation Number
          {{#if registrationNumber}}{{registrationNumber}}{{else}}[Registration Number]{{/if}}, and registered address
          at:</p>
        <p class="address">
          {{#if address1}}{{address1}}{{/if}}{{#if address2}}, {{address2}}{{/if}},<br />
          {{#if city}}{{city}},{{/if}}
          {{#if state}}{{state}},{{/if}}
          {{#if zip}}{{zip}}{{/if}}<br />
          {{#if country}}{{country}}{{/if}}
        </p>
        <p class="bold-text">by its undersigned Authorized Principal</p>
        <p class='principal-name'>{{#if principalName}}{{principalName}}{{else}}[Principal Name]{{/if}}</p>
        <p class="residing-at">Residing at:</p>
        <p class="address">
          {{#if principalAddress1}}{{principalAddress1}}{{/if}}{{#if principalAddress2}}, {{principalAddress2}}{{/if}},<br />
          {{#if principalCity}}{{principalCity}},{{/if}}
          {{#if principalState}}{{principalState}},{{/if}}
          {{#if principalZip}}{{principalZip}}{{/if}}<br />
          {{#if principalCountry}}{{principalCountry}}{{/if}}
        </p>
        <p>This Merchant Agreement fully incorporates by reference the Merchant Application and information therein
          submitted on
          {{date}}
          with Merchant Application Form - Acquiring, Submission ID
          {{#if submissionId}}{{submissionId}}{{else}}[Submission ID]{{/if}}, which shall be deemed an integral and
          inseparable part hereof and hereby also referred to as "This Merchant Application".</p>
      </div>

      <!-- Industry section -->
      <div class='section'>
        <div class='section-title'>Industry</div>
        <p>
          {{#if industryBusinessCategory.length}}
            {{#each industryBusinessCategory}}
              {{this}}<br />
            {{/each}}
          {{/if}}
        </p>
      </div>

      <!-- Target Markets and Distribution Section -->
      {{#if targetMarketsAndDistribution}}
        <div class='section markets-section'>
          <div class='section-title'>Target Markets and Distribution</div>

          <style>
            .markets-table {
              width: 100%;
              border-collapse: collapse;
            }
            .markets-header {
              font-weight: bold;
              text-align: left;
              padding-right: 20px;
            }
            .markets-cell {
              vertical-align: top;
              padding: 4px 20px 0 0;
            }
            .markets-item {
              padding: 2px 20px 0px 0px;
              display: inline-block;
              font-size: 13px;
            }
          </style>

          <table class='markets-table'>
            <tr>
              <th class='markets-header'>Country</th>
              <th class='markets-header'>Percentage</th>
            </tr>
            {{#each targetMarketsAndDistribution}}
              {{#with this as |item|}}
                <tr>
                  {{#if item.country}}
                  <td class='markets-cell'>
                    <span class='markets-item'>{{item.country}}</span>
                  </td>
                  {{/if}}
                  {{#if item.volumePercentage}}
                  <td class='markets-cell'>
                    <span class='markets-item'>{{item.volumePercentage}}%</span>
                  </td>
                </tr>
                {{/if}}
              {{/with}}
            {{/each}}
          </table>
        </div>
      {{/if}}

      <!-- Settlement Bank Accounts Section -->
      {{#if bankSettlement}}
        <div class='section bank-section'>
          <div class='section-title'>Settlement Bank Accounts</div>

          <style>
            .bank-currency-block {
              margin-bottom: 20px;
            }
            .bank-currency-header {
              font-size: 12pt;
              font-weight: bold;
            }
            .bank-currency-name {
              padding-top: 10px;
              font-weight: bold;
              display: inline-block;
            }
            .bank-details-table {
              width: 100%;
              margin-top: 10px;
            }
            .bank-detail-label {
              padding-top: 5px;
              font-weight: bold;
              width: 200px;
              font-size: 13px;
            }
            .bank-detail-value {
              padding-top: 5px;
              font-size: 13px;
            }
          </style>

          {{#each bankSettlement}}
            {{#with this as |item|}}
              <div class='bank-currency-block'>
                <div class='bank-currency-header'>
                  <span class='bank-currency-name'>{{#if this.currency}}{{this.currency}}{{/if}}</span> settlement bank
                </div>

                <table class='bank-details-table'>
                  {{#if this.bankCountry}}
                  <tr>
                    <td class='bank-detail-label'>Bank country</td>
                    <td class='bank-detail-value'>{{this.bankCountry}}</td>
                  </tr>
                  {{/if}}
                  {{#if this.bankName}}
                  <tr>
                    <td class='bank-detail-label'>Bank Name</td>
                    <td class='bank-detail-value'>{{this.bankName}}</td>
                  </tr>
                  {{/if}}
                  {{#if this.bankIban}}
                  <tr>
                    <td class='bank-detail-label'>Account Number / IBAN</td>
                    <td class='bank-detail-value'>{{this.bankIban}}</td>
                  </tr>
                  {{/if}}
                  {{#if this.bankCode}}
                  <tr>
                    <td class='bank-detail-label'>SWIFT/BIC Code / Routing Number / Sort Code</td>
                    <td class='bank-detail-value'>{{this.bankCode}}</td>
                  </tr>
                  {{/if}}
                  {{#if this.bankAccountHolder}}
                  <tr>
                    <td class='bank-detail-label'>Account Holder Name</td>
                    <td class='bank-detail-value'>{{this.bankAccountHolder}}</td>
                  </tr>
                  {{/if}}
                </table>
              </div>
            {{/with}}
          {{/each}}
        </div>
      {{/if}}

      <!-- Websites section -->
      <div class='section'>
        <div class='section-title'>Websites</div>

        {{#if websites.length}}
          {{#each websites}}
            <div class='website-section'>
              <div class='website-title'>Website {{add @index 1}}</div>

              {{#if this.mccClassification}}
                {{#if this.mccClassification.mcc}}
                  <div class='field-row'>
                    <span class='field-label'>MCC:</span>
                    <span>{{this.mccClassification.mcc}}</span>
                  </div>
                {{/if}}
              {{/if}}

              <div class='field-row'>
                <span class='field-label'>URL:</span>
                <span>{{#if this.url}}{{this.url}}{{else}}[Website URL]{{/if}}</span>
              </div>

              {{#if this.statementDescriptor}}
                <div class='field-row'>
                  <span class='field-label'>Statement Descriptor:</span>
                  <span style='white-space: nowrap;'>{{this.statementDescriptor}}</span>
                </div>
              {{/if}}

              {{#if this.cityField}}
                <div class='field-row'>
                  <span class='field-label'>City Field:</span>
                  <span>{{this.cityField}}</span>
                </div>
              {{/if}}
            </div>
          {{/each}}
        {{/if}}
      </div>

      <!-- Cryptocurrency Settlements Section -->
      {{#if cryptoSettlement}}
        <div class='section crypto-section'>
          <div class='section-title'>Cryptocurrency Settlements</div>

          <style>
            .crypto-currency-block {
              margin-bottom: 20px;
            }
            .crypto-currency-header {
              font-size: 13pt;
              font-weight: bold;
              margin-bottom: 15px;
            }
            .crypto-currency-name {
              padding-top: 10px;
              font-weight: bold;
              display: inline-block;
            }
            .crypto-details-table {
              width: 100%;
              margin-top: 10px;
            }
            .crypto-detail-label {
              padding-top: 5px;
              font-weight: bold;
              width: 200px;
              font-size: 13px;
            }
            .crypto-detail-value {
              padding-top: 5px;
              font-size: 13px;
            }
          </style>

          {{#each cryptoSettlement}}
            {{#with this as |item|}}
              <div class='crypto-currency-block'>
                <div class='crypto-currency-header'>
                  <span class='crypto-currency-name'>{{#if this.currency}}{{this.currency}}{{/if}}</span> to Cryptocurrency Settlement
                </div>

                <table class='crypto-details-table'>
                  {{#if this.cryptocurrency}}
                  <tr>
                    <td class='crypto-detail-label'>Cryptocurrency</td>
                    <td class='crypto-detail-value'>{{this.cryptocurrency}}</td>
                  </tr>
                  {{/if}}
                  {{#if this.percentage}}
                  <tr>
                    <td class='crypto-detail-label'>Percentage</td>
                    <td class='crypto-detail-value'>{{this.percentage}}%</td>
                  </tr>
                  {{/if}}
                  {{#if this.walletAddress}}
                  <tr>
                    <td class='crypto-detail-label'>Wallet Address</td>
                    <td class='crypto-detail-value'>{{this.walletAddress}}</td>
                  </tr>
                  {{/if}}
                  {{#if this.preferredNetwork}}
                  <tr>
                    <td class='crypto-detail-label'>Preferred Network</td>
                    <td class='crypto-detail-value'>{{this.preferredNetwork}}</td>
                  </tr>
                  {{/if}}
                </table>
              </div>
            {{/with}}
          {{/each}}
      </div>
      {{/if}}

      <div class='section'>
        <div class='section-title'>Processing and Settlement Currencies</div>
        <style>
          .currencies-table {
            width: 100%;
            border-collapse: collapse;
          }
          .currency-header {
            font-weight: bold;
            text-align: left;
            padding-bottom: 15px;
            padding-right: 20px;
          }
          .currency-cell {
            vertical-align: top;
            padding-right: 20px;
          }
          .currency-item {
            margin-bottom: 5px;
            font-size: 13px;
            display: inline-block;
          }
        </style>
        <table class='currencies-table'>
          <tr>
            <!-- Headers in one row - only show if there's data -->
            {{#if currencies.length}}
              <th class='currency-header'>Processing</th>
            {{/if}}

            {{#if (anyNotIncludes settlementCurrencies ' ')}}
              <th class='currency-header'>FIAT Settlement</th>
            {{/if}}

            {{#if (anyIncludes settlementCurrencies ' ')}}
              <th class='currency-header'>Crypto Settlement</th>
            {{/if}}
          </tr>
          <tr>
            <!-- Processing Currencies Column -->
            {{#if currencies.length}}
              <td class='currency-cell'>
                {{#each currencies}}
                  <div class='currency-item'>{{this}}</div><br />
                {{/each}}
              </td>
            {{/if}}

            <!-- FIAT Settlement Column -->
            {{#if (anyNotIncludes settlementCurrencies ' ')}}
              <td class='currency-cell'>
                {{#each settlementCurrencies}}
                  {{#unless (includes this ' ')}}
                    <div class='currency-item'>{{this}}</div><br />
                  {{/unless}}
                {{/each}}
              </td>
            {{/if}}

            <!-- Crypto Settlement Column -->
            {{#if (anyIncludes settlementCurrencies ' ')}}
              <td class='currency-cell'>
                {{#each settlementCurrencies}}
                  {{#if (includes this ' ')}}
                    <div class='currency-item'>{{this}}</div><br />
                  {{/if}}
                {{/each}}
              </td>
            {{/if}}
          </tr>
        </table>
      </div>

      <!-- Ticket Size -->
      {{!-- <div class='section markets-section'>
        <div class='section-title'>Ticket Size</div>

        <style>
          .ticket-table { width: 100%; margin-top: 20px; border-collapse: collapse; }
          .ticket-cell-label { text-align: left; padding: 8px 20px 0 0; }
          .ticket-cell-value { text-align: center; padding: 8px 0; }
          .ticket-item { padding: 4px 20px 0px 0px; display: inline-block; }
        </style>

        <table class='ticket-table'>
          <tr>
            <td class='ticket-cell-label'>
              <span class='ticket-item'>Minimum Transaction Amount</span>
            </td>
            <td class='ticket-cell-value'>
              <span class='ticket-item'>{{minTransactionAmount}}</span>
            </td>
          </tr>
          <tr>
            <td class='ticket-cell-label'>
              <span class='ticket-item'>Maximum Transaction Amount</span>
            </td>
            <td class='ticket-cell-value'>
              <span class='ticket-item'>{{maxTransactionAmount}}</span>
            </td>
          </tr>
        </table>
      </div> --}}
    </div>
  </body>
</html>