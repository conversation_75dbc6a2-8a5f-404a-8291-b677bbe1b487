import { Principal } from '../../../interfaces/principalInterface';

const mockPrincipalModel = {
  create: jest.fn(),
  findById: jest.fn(),
  findOne: jest.fn(),
  findByIdAndUpdate: jest.fn(),
  findByIdAndDelete: jest.fn(),
  paginate: jest.fn()
};

export default mockPrincipalModel;

export interface PrincipalModelDocument extends Principal {
  _id: string;
  save: jest.Mock;
  toObject: jest.Mock;
}
