import { AcquiringEntity } from '../../../interfaces/acquiringEntityInterface';

const mockAcquiringEntityModel = {
  create: jest.fn(),
  findById: jest.fn(),
  findOne: jest.fn(),
  findByIdAndUpdate: jest.fn(),
  findByIdAndDelete: jest.fn(),
  paginate: jest.fn()
};

export default mockAcquiringEntityModel;

export interface AcquiringEntityModelDocument extends AcquiringEntity {
  _id: string;
  save: jest.Mock;
  toObject: jest.Mock;
}
