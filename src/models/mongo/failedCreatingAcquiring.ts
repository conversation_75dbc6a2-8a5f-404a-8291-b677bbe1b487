import mongoose, { Schema, Types } from 'mongoose';
import paginate from 'mongoose-paginate-v2';

import { FailedCreatingAcquiring } from '../../interfaces/failedCreatingAcquiring';

const FailedCreatingAcquiringSchema: Schema = new Schema<FailedCreatingAcquiring>(
  {
    receivedData: { type: Schema.Types.Mixed, required: false },
    error: { type: String, required: true }
  },
  { timestamps: true }
);
FailedCreatingAcquiringSchema.plugin(paginate);
export interface FailedCreatingAcquiringModelDocument extends mongoose.Document, FailedCreatingAcquiring {}

const FailedCreatingAcquiringModel = mongoose.model<
  FailedCreatingAcquiringModelDocument,
  mongoose.PaginateModel<FailedCreatingAcquiringModelDocument>
>('failed_creating_acquiring', FailedCreatingAcquiringSchema);

export default FailedCreatingAcquiringModel;
