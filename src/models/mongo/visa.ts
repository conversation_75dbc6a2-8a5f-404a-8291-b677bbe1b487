import { Schema } from 'mongoose';
import { VisaMatchDataItem, VisaMatchData } from '../../interfaces/visaVmssInterface';

const VisaMatchDataItemSchema = new Schema<VisaMatchDataItem>(
  {
    url: { type: String, required: false },
    isMatch: { type: Boolean, required: false },
    resolvedVisaMatch: { type: Boolean, required: false },
    visaTotalCountMatches: { type: Number, required: false },
    visaResultData: { type: Schema.Types.Mixed, required: false },
    errorMessage: { type: Schema.Types.Mixed, required: false }
  },
  { timestamps: true }
);

const VisaMatchDataSchema = new Schema<VisaMatchData>(
  {
    requestMadeToCheckMerchant: { type: Number, required: false },
    data: { type: [VisaMatchDataItemSchema], required: false },
    errorMessageRequest: { type: Schema.Types.Mixed, required: false },
    failedRequest: { type: Number, required: false },
    isThereAnyMatch: { type: Boolean, required: false },
    resolvedVisaMatch: { type: Boolean, required: false }
  },
  { timestamps: true }
);

export default VisaMatchDataSchema;
