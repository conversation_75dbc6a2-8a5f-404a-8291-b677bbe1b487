import mongoose, { Schema, Types } from 'mongoose';
import paginate from 'mongoose-paginate-v2';

import { Principal } from '../../interfaces/principalInterface';
import { PrincipalType } from '../../types/principal';
import { SumsubSchema } from './acquiringEntity';

const PrincipalSchema: Schema = new Schema<Principal>(
  {
    firstName: { type: String, required: true },
    middleName: { type: String, required: false },
    lastName: { type: String, required: true },
    email: { type: String, required: false },
    phone: { type: String, required: false },
    // Country to be full name NOT 2 or 3 letters code
    country: { type: String, required: false },
    state: { type: String, required: false },
    city: { type: String, required: false },
    zip: { type: String, required: false },
    address1: { type: String, required: false },
    address2: { type: String, required: false },
    dateOfBirth: { type: String, required: false },
    passportNumber: { type: String, required: false },
    driverLicenseNumber: { type: String, required: false },
    positionInCompany: { type: String, enum: Object.values(PrincipalType), required: false },
    acquiringEntityId: { type: Types.ObjectId, ref: 'AcquiringEntity', required: false },
    sumsub: { type: SumsubSchema, required: false },
    connectBeneficiaryToCompanyError: { type: Schema.Types.Mixed, required: false }
  },
  { timestamps: true }
);
PrincipalSchema.plugin(paginate);
export interface PrincipalModelDocument extends mongoose.Document, Principal {}

const PrincipalModel = mongoose.model<PrincipalModelDocument, mongoose.PaginateModel<PrincipalModelDocument>>(
  'principal',
  PrincipalSchema
);

export default PrincipalModel;
