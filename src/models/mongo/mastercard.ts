import { MastercardMatchDataItem, MastercardMatchData } from 'src/interfaces/mastercardMatchProInterfaces';
import { Schema } from 'mongoose';

const MastercardMatchDataItemSchema = new Schema<MastercardMatchDataItem>(
  {
    url: { type: String, required: false },
    isMatch: { type: Boolean, required: false },
    resolvedMastercardMatch: { type: Boolean, required: false },
    mastercardTotalCountMatches: { type: Number, required: false },
    mastercardRefNumberMatched: { type: String, required: false },
    errorMessage: { type: Schema.Types.Mixed, required: false }
  },
  { timestamps: true }
);

const MastercardMatchDataSchema = new Schema<MastercardMatchData>(
  {
    requestMadeToCheckMerchant: { type: Number, required: false },
    data: { type: [MastercardMatchDataItemSchema], required: false },
    errorMessageRequest: { type: Schema.Types.Mixed, required: false },
    failedRequest: { type: Number, required: false },
    isThereAnyMatch: { type: Boolean, required: false },
    resolvedMastercardMatch: { type: Boolean, required: false }
  },
  { timestamps: true }
);

export default MastercardMatchDataSchema;
