import mongoose, { Schema, Types } from 'mongoose';
import paginate from 'mongoose-paginate-v2';

import {
  AcquiringEntity,
  SumsubData,
  Website,
  AdditionalData,
  MidData,
  CryptoSettlement,
  TargetMarketsAndDistribution,
  BankSettlement,
  BusinessOffering,
  IndustryBusinessCategory,
  SalesMetrics,
  ShippingInfo,
  RdrSettings,
  MasterCardSettings,
  AutomaticRefunds,
  IntegrationsRequirements,
  SettlementAccountMetrics,
  VolumeRange,
  Contact,
  CryptoIndustry,
  MSBIndustry,
  AdultServices,
  DatingEscortData,
  IndustrySpecificStatement,
  License,
  History,
  DataChanged,
  AciResult,
  OmnipayResult
} from '../../interfaces/acquiringEntityInterface';
import { BusinessCategoryVmss } from '../../enums/businessCategory';
import { MCCClassification } from 'src/interfaces/mccInterface';
import MastercardMatchDataSchema from './mastercard';
import VisaMatchDataSchema from './visa';
import { AcquiringEntityEnum } from '../../enums/acquringEntityEnum';

import { ReviewStatus, ReviewAnswer } from '../../enums/sumsub';
export const SumsubSchema = new Schema<SumsubData>(
  {
    sumsubId: { type: String, required: false },
    status: { type: String, required: false },
    reviewStatus: { type: String, enum: ReviewStatus, required: false },
    reviewAnswer: { type: String, enum: ReviewAnswer, required: false },
    levelName: { type: String, required: false },
    createApplicantError: { type: Schema.Types.Mixed, required: false },
    AMLError: { type: Schema.Types.Mixed, required: false },
    additionalErrors: { type: Schema.Types.Mixed, required: false }
  },
  { _id: false, timestamps: true }
);

const MccClassifierSchema = new Schema<MCCClassification>(
  {
    url: { type: String, required: false, default: null },
    mcc: { type: String, required: false, default: null },
    certainty: { type: Number, required: false },
    description: { type: String, required: false },
    errorMessage: { type: String, required: false },
    retryCount: { type: Number, required: false }
  },
  { _id: false, timestamps: true }
);

const WebsitesSchema = new Schema<Website>(
  {
    url: { type: String, required: false },
    isWebsiteClassified: { type: Boolean, required: false, default: false },
    mccClassification: { type: MccClassifierSchema, required: false, default: () => ({}) },
    sumsub: { type: SumsubSchema, required: false },
    statementDescriptor: { type: String, required: false },
    cityField: { type: String, required: false }
  },
  { _id: false }
);

const CryptoSettlementSchema = new Schema<CryptoSettlement>(
  {
    currency: { type: String, required: false },
    percentage: { type: String, required: false },
    walletAddress: { type: String, required: false },
    preferredNetwork: { type: String, required: false },
    cryptocurrency: { type: String, required: false }
  },
  { _id: false }
);

const BankSettlementSchema = new Schema<BankSettlement>(
  {
    currency: { type: String, required: false },
    bankCountry: { type: String, required: false },
    bankName: { type: String, required: false },
    bankIban: { type: String, required: false },
    bankCode: { type: String, required: false },
    bankAccountHolder: { type: String, required: false }
  },
  { _id: false }
);

const IndustryBusinessCategorySchema = new Schema<IndustryBusinessCategory>(
  {
    category: { type: String, required: false },
    subcategory: { type: [String], required: false }
  },
  { _id: false }
);

const BusinessOfferingSchema = new Schema<BusinessOffering>(
  {
    industryBusinessCategory: { type: [IndustryBusinessCategorySchema], required: false },
    productOrServicesDescription: { type: String, required: false }
  },
  { _id: false }
);

const TargetMarketsAndDistributionSchema = new Schema<TargetMarketsAndDistribution>(
  {
    country: { type: String, required: false },
    volumePercentage: { type: String, required: false }
  },
  { _id: false }
);

const SalesMetricsSchema = new Schema<SalesMetrics>(
  {
    totalMonthlySalesVolume: { type: String, required: false },
    expectedMonthlyVolume: { type: String, required: false },
    numberOfExpectedMonthlyTransactions: { type: String, required: false },
    averageTransactionAmount: { type: String, required: false },
    highestTransactionAmount: { type: String, required: false },
    lowestTransactionAmount: { type: String, required: false }
  },
  { _id: false }
);

const ShippingInfoSchema = new Schema<ShippingInfo>(
  {
    shippingMethods: { type: String, required: false },
    trackingNumber: { type: String, required: false },
    shippingFeesAndDeliveryTimes: { type: String, required: false }
  },
  { _id: false }
);

const AutomaticRefundsSchema = new Schema<AutomaticRefunds>(
  {
    option: { type: String, required: false },
    thresholdAmount: { type: Number, required: false }
  },
  { _id: false }
);

const RdrSettingsSchema = new Schema<RdrSettings>(
  {
    rdrEnrollmentPreference: { type: String, required: false },
    automaticRdrRefunds: { type: AutomaticRefundsSchema, required: false }
  },
  { _id: false }
);

const MasterCardSettingsSchema = new Schema<MasterCardSettings>(
  {
    masterCardEnrollmentPreference: { type: String, required: false },
    automaticMasterCardRefunds: { type: AutomaticRefundsSchema, required: false }
  },
  { _id: false }
);

const IntegrationsRequirementsSchema = new Schema<IntegrationsRequirements>(
  {
    integrationType: { type: String, required: false },
    pciComplianceStatus: { type: String, required: false },
    thirdPartyGateway: { type: String, required: false, default: null },
    integrationOption: { type: String, required: false, default: null },
    paymentPageHostingPreference: { type: String, required: false },
    paymentPageURL: { type: String, required: false },
    mandatory3ds: { type: Boolean, required: false, default: null }
  },
  { _id: false }
);

const VolumeRangeSchema = new Schema<VolumeRange>(
  {
    incoming: { type: String, required: false },
    outgoing: { type: String, required: false }
  },
  { _id: false }
);

const SettlementAccountMetricsSchema = new Schema<SettlementAccountMetrics>(
  {
    monthlySalesVolume: { type: String, required: false },
    estimatedMonthlyTransfers: { type: String, required: false },
    billingMethods: { type: [String], required: false },
    processedCreditCardsBefore: { type: String, required: false },
    fraudAndChargebacksRates: { type: [[String]], required: false },
    processingAndChargebacksHistory: { type: [Schema.Types.Mixed], required: false },
    isRevenueFromBusinessActivity: { type: String, required: false },
    freeTrialZeroAmount: { type: String, required: false, default: null }
  },
  { _id: false }
);

const ContactSchema = new Schema<Contact>(
  {
    name: { type: String, required: false, default: null },
    email: { type: String, required: false, default: null },
    phone: { type: String, required: false, default: null },
    other: { type: String, required: false, default: null }
  },
  { _id: false }
);

const LicenseSchema = new Schema<License>(
  {
    number: { type: String, required: false, default: null },
    website: { type: String, required: false, default: null },
    issuingBody: { type: String, required: false, default: null }
  },
  { _id: false }
);

const CryptoIndustrySchema = new Schema<CryptoIndustry>(
  {
    cryptoActivityType: { type: [String], required: false },
    cryptocurrenciesList: { type: [String], required: false },
    nativeTokensSold: { type: Boolean, required: false },
    nativeTokensTypes: { type: [String], required: false, default: null },
    customerBase: { type: [String], required: false },
    conversions: { type: [String], required: false },
    KYCStages: { type: [String], required: false },
    tools: { type: Boolean, required: false, default: null },
    withdrawals: { type: Boolean, required: false },
    withdrawalsWalletTypes: { type: [String], required: false, default: null },
    withdrawalsProcess: { type: String, required: false },
    risks: { type: Boolean, required: false },
    yearsEngaging: { type: String, required: false },
    training: { type: Boolean, required: false },
    pastTransactions: { type: String, required: false },
    professionalBackground: { type: String, required: false }
  },
  { _id: false }
);

const MSBIndustrySchema = new Schema<MSBIndustry>(
  {
    employeesNumber: { type: String, required: false },
    annualTurnover: { type: String, required: false },
    serviceTypes: { type: [String], required: false },
    customerBase: { type: [String], required: false },
    internationalTransactionsRatio: { type: String, required: false },
    enhancedDueDiligence: { type: Boolean, required: false, default: null },
    KYCStages: { type: [String], required: false },
    customerIDVerification: { type: [String], required: false },
    preventionSystem: { type: Boolean, required: false, default: null },
    complianceEmployeesNumber: { type: String, required: false },
    highRiskClientsRatio: { type: String, required: false },
    complianceAudits: { type: String, required: false },
    cryptoInvolvement: { type: Boolean, required: false, default: null },
    cryptoExposureDetails: { type: String, required: false, default: null }
  },
  { _id: false }
);

const AdultServicesSchema = new Schema<AdultServices>(
  {
    contentSources: { type: [String], required: false },
    contentConsentAgreements: { type: Boolean, required: false, default: null },
    contentVerificationMethods: { type: String, required: false },
    contentPoviderAgeAndIDVerification: { type: String, required: false },
    contentReviewProcess: { type: String, required: false },
    liveStreamingControl: { type: Boolean, required: false },
    liveStreamingMonitoringAndRemoval: { type: String, required: false },
    removalAppealProcess: { type: String, required: false },
    marketingAndSearchTerms: { type: String, required: false },
    potentiallyIllegalContentPreventionProcess: { type: String, required: false },
    potentiallyIllegalContentComplaintProcess: { type: Boolean, required: false },
    illegalContentReporting: { type: Boolean, required: false },
    traffickingAndAbusePolicy: { type: String, required: false },
    consentAgreementsRetention: { type: Boolean, required: false },
    offerAIContent: { type: Boolean, required: false, default: null },
    AIContentSources: { type: String, required: false, default: null },
    AIContentAgreements: { type: String, required: false, default: null },
    confirmNoAIDeepfakes: { type: Boolean, required: false, default: null }
  },
  { _id: false }
);

const DatingEscortDataSchema = new Schema<DatingEscortData>(
  {
    antiTraffickingProgram: { type: String, required: false },
    contentReviewProcess: { type: String, required: false },
    contentRemovalAppeals: { type: String, required: false },
    marketingAndSearchTermsCompliance: { type: String, required: false },
    contentProviderConsentsDepiction: { type: String, required: false },
    consentPublicDistribution: { type: String, required: false },
    consentContentDownload: { type: String, required: false },
    consentsValidationAndStorage: { type: String, required: false },
    evidenceValidationAndRetention: { type: String, required: false },
    marketingStrategy: { type: String, required: false },
    fakeBotPrevention: { type: String, required: false }
  },
  { _id: false }
);

const IndustrySpecificStatementSchema = new Schema<IndustrySpecificStatement>(
  {
    highValueKYC: { type: Boolean, required: false },
    insurance: { type: Boolean, required: false },
    highValuePlayerKYC: { type: Boolean, required: false },
    payoutReserves: { type: Boolean, required: false },
    bonusAbusePrevention: { type: Boolean, required: false },
    selfExclusionOrResponsibleTools: { type: Boolean, required: false },
    coolingOff: { type: Boolean, required: false },
    highValueGoods: { type: Boolean, required: false },
    gamblingInclusion: { type: Boolean, required: false },
    childProtection: { type: Boolean, required: false },
    ageVerification: { type: Boolean, required: false },
    guestOrAnnonymousAccounts: { type: Boolean, required: false },
    spendingLimits: { type: Boolean, required: false },
    tieredAccountSystem: { type: Boolean, required: false },
    kycStages: { type: String, required: false },
    forexLicenseHolder: { type: String, required: false },
    gamblingLicense: { type: String, required: false },
    geofencing: { type: String, required: false },
    fraudPrevention: { type: String, required: false },
    minimumAgeRequirements: { type: String, required: false },
    payoutWithdrawals: { type: String, required: false },
    cardRevenueRatio: { type: String, required: false },
    suspiciousPatternsPrevention: { type: String, required: false },
    massBettingPrevention: { type: String, required: false },
    singleOrMultiVendor: { type: String, required: false },
    transacitonProcessing: { type: String, required: false },
    disputeResolution: { type: String, required: false },
    vendorsNumber: { type: String, required: false },
    suspiciousAccountHandling: { type: String, required: false },
    payoutReserveManagement: { type: String, required: false, default: null },
    selfExclusionOrResponsibleToolsDescription: { type: String, required: false, default: null },
    consumerProtectionMethods: { type: [String], required: false },
    marketplaceTypes: { type: [String], required: false },
    vendorOnboarding: { type: [String], required: false },
    gamingServiceTypes: { type: [String], required: false },
    currencyTypes: { type: [String], required: false },
    transactionTypes: { type: [String], required: false },
    accountCreationInformation: { type: [String], required: false },
    fraudPreventionGmg: { type: [String], required: false },
    digWalTypes: { type: [String], required: false },
    serviceTypesDigWal: { type: [String], required: false },
    targetAudience: { type: [String], required: false },
    onboardingInformation: { type: [String], required: false },
    IDVerification: { type: [String], required: false }
  },
  { _id: false }
);

const AdditionalDataSchema = new Schema<AdditionalData>(
  {
    currencies: { type: [String], required: false },
    settlementCurrencies: { type: [String], required: false },
    integrationsRequirements: { type: IntegrationsRequirementsSchema, required: false },
    businessOffering: { type: BusinessOfferingSchema, required: false },
    cryptoSettlement: { type: [CryptoSettlementSchema], required: false },
    targetMarketsAndDistribution: { type: [TargetMarketsAndDistributionSchema], required: false },
    bankSettlement: { type: [BankSettlementSchema], required: false },
    salesMetrics: { type: SalesMetricsSchema, required: false },
    shippingInfo: { type: ShippingInfoSchema, required: false },
    rdrSettings: { type: RdrSettingsSchema, required: false },
    masterCardSettings: { type: MasterCardSettingsSchema, required: false },
    settlementAccountMetrics: { type: SettlementAccountMetricsSchema, required: false },
    contacts: { type: Map, of: ContactSchema, required: false },
    cryptoIndustry: { type: CryptoIndustrySchema, required: false },
    MSBIndustry: { type: MSBIndustrySchema, required: false },
    adultServices: { type: AdultServicesSchema, required: false },
    datingEscortData: { type: DatingEscortDataSchema, required: false },
    industrySpecificStatement: { type: IndustrySpecificStatementSchema, required: false },
    licenses: { type: Map, of: LicenseSchema, required: false }
  },
  { _id: false }
);

const MidDataSchema = new Schema<MidData>(
  {
    longTerminal: { type: String, required: true },
    parentLongTerminal: { type: String, required: true },
    nextLongTerminal: { type: String, required: false, default: '' },
    isMasterMid: { type: Boolean, required: true },
    error: { type: String, required: false }
  },
  { _id: false }
);

const ChildLongTerminalSchema = new Schema(
  {
    longTerminal: { type: String, required: true },
    currency: { type: String, required: true }
  },
  { _id: false }
);

const OmnipayResultSchema = new Schema<OmnipayResult>(
  {
    longTerminal: { type: String, required: false },
    isMasterMid: { type: Boolean, required: false },
    childLongTerminals: { type: [ChildLongTerminalSchema], required: false },
    error: { type: String, required: false }
  },
  { _id: false }
);

const OnboardedEntitySchema = new Schema(
  {
    longTerminal: { type: String, required: true },
    currency: { type: String, required: true }
  },
  { _id: false }
);

const AciResultSchema = new Schema<AciResult>(
  {
    onboardedEntities: { type: [OnboardedEntitySchema], required: true },
    error: { type: String, required: false }
  },
  { _id: false }
);

const DataChangedSchema = new Schema<DataChanged>(
  {
    field: { type: String, required: true },
    oldValue: { type: Schema.Types.Mixed, required: false },
    newValue: { type: Schema.Types.Mixed, required: false }
  },
  { _id: false }
);
const HistorySchema = new Schema<History>(
  {
    dataChanged: { type: [DataChangedSchema], required: true },
    changedBy: { type: String, required: false },
    changedAt: { type: Date, default: Date.now }
  },
  { _id: false }
);

const AcquiringEntitySchema: Schema = new Schema<AcquiringEntity>(
  {
    name: { type: String, required: true },
    entityType: {
      type: String,
      required: true,
      enum: Object.values(AcquiringEntityEnum),
      default: AcquiringEntityEnum.MERCHANT
    },
    pspIsoReferralAgentName: { type: String, required: false },
    registrationNumber: { type: String, required: true, unique: true },
    taxId: { type: String, required: false },
    incorporationDate: { type: String, required: false },
    email: { type: String, required: false },
    phone: { type: [String], required: false },
    // Country to be full name NOT 2 or 3 letters code
    country: { type: String, required: false },
    state: { type: String, required: false },
    city: { type: String, required: false },
    zip: { type: String, required: false },
    address1: { type: String, required: false },
    address2: { type: String, required: false },
    tradeOverInternet: { type: Boolean, required: false, default: false },
    websites: { type: [WebsitesSchema], required: false },
    isAllWebsitesClassified: { type: Boolean, required: false, default: false },
    sumsub: { type: SumsubSchema, required: false },
    // principalsIds need to be equal to the length of the principalRawData,
    // because if it is not equal the some principal is not created or missing
    principalsIds: [{ type: Types.ObjectId, ref: 'principal', required: false }],
    principalRawData: [
      {
        type: Schema.Types.Mixed,
        required: false
      }
    ],
    businessCategoryVmss: {
      type: String,
      required: false,
      enum: Object.values(BusinessCategoryVmss),
      default: BusinessCategoryVmss.MERCHANT
    },
    tradingName: { type: String, required: false },
    address1TradingAs: { type: String, required: false },
    address2TradingAs: { type: String, required: false },
    cityTradingAs: { type: String, required: false },
    stateTradingAs: { type: String, required: false },
    zipTradingAs: { type: String, required: false },
    countryTradingAs: { type: String, required: false },
    submissionId: { type: String, required: false },
    visaMatchData: { type: [VisaMatchDataSchema], required: false },
    mastercardMatchData: { type: [MastercardMatchDataSchema], required: false },
    additionalData: { type: AdditionalDataSchema, required: false },
    onboardingError: { type: Schema.Types.Mixed, required: false },
    correspondenceEmail: { type: String, required: false },
    midConfiguration: { type: MidDataSchema, required: false },
    omnipayResult: { type: OmnipayResultSchema, required: false },
    aciResult: { type: AciResultSchema, required: false },
    history: { type: [HistorySchema], required: false, default: [] }
  },
  { timestamps: true }
);
AcquiringEntitySchema.plugin(paginate);
export interface AcquiringEntityModelDocument extends mongoose.Document, AcquiringEntity {}

const AcquiringEntityModel = mongoose.model<
  AcquiringEntityModelDocument,
  mongoose.PaginateModel<AcquiringEntityModelDocument>
>('acquiring_entity', AcquiringEntitySchema);

export default AcquiringEntityModel;
