import express from 'express';
import logger from '../utils/logger';
import { createAcquiringEntityController } from '../controllers/acquiringEntityController';

const routes = express.Router();

// Health-check endpoint
routes.get('', function (_req: express.Request, res: express.Response) {
  logger.info(`Health-check probe/readiness: ${Date.now()}`);
  res.status(200).send('OK');
});

routes.post('/acquiring-entity', createAcquiringEntityController);

export default routes;
