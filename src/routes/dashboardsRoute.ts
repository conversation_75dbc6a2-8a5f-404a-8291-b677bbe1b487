import validateRequest from '@submodules/ryvyl-commons/middlewares/validateReqMiddleware';
import { authenticateJWT } from '@submodules/ryvyl-commons/middlewares/authMiddleware';
import {
  checkMastercardMatchProController,
  getTerminationHistoryByReferenceController,
  mastercardMatchProAllMatchReferenceDashboardController,
  mastercardMatchProDashboardController
} from '../controllers/mastercardController';
import express from 'express';
import {
  postAcquiringEntityUpdateController,
  getAcquiringEntityWithAllPropertiesController,
  visaVmssHandleManualAcquiringEntityController,
  mastercardMatchProHandleManualAcquiringEntityByRefNumberController,
  mastercardMatchProHandleManualAcquiringEntityController,
  getAcquiringEntities,
  createAcquiringEntityDashboardController,
  retryToOnboardController
} from '../controllers/acquiringEntityController';
import {
  manualApproveAcquireByRefNumberSchema,
  manualApproveAcquireSchema
} from '../schemaValidations/acquiringEntitySchemaValidation';
import {
  checkAcquiringEntityMatchInVmssController,
  checkVisaVmssController,
  getVisaVmssMatchDashboardController
} from '../controllers/visaController';
import { getPrincipalDefaultValuesController } from '../controllers/principalController';
import { processMidConfigurationController } from '../controllers/acquiringEntityController';

const dashboardsRoute = express.Router();
// To check acquiring entity match in mastercard
dashboardsRoute.get(
  '/mastercard-match-pro-termination-inquiries/:referenceNumber',
  getTerminationHistoryByReferenceController
);
dashboardsRoute.post('/mastercard-match-pro', authenticateJWT, checkMastercardMatchProController);
dashboardsRoute.post('/visa-vmss-match', authenticateJWT, checkVisaVmssController);
dashboardsRoute.get('/visa-vmss-match/:acquiringEntityId', authenticateJWT, checkAcquiringEntityMatchInVmssController);
dashboardsRoute.get('/acquiring-entities', getAcquiringEntities);
dashboardsRoute.get(
  '/mastercard-match-pro-dashboard/:acquiringEntityId',
  authenticateJWT,
  mastercardMatchProDashboardController
);
dashboardsRoute.get(
  '/mastercard-match-pro-all-match-reference-dashboard/:acquiringEntityId',
  authenticateJWT,
  mastercardMatchProAllMatchReferenceDashboardController
);
dashboardsRoute.get(
  '/visa-vmss-match-dashboard/:acquiringEntityId',
  authenticateJWT,
  getVisaVmssMatchDashboardController
);
dashboardsRoute.post(
  '/mastercard-match-pro-handle-manual-acquire',
  authenticateJWT,
  validateRequest(manualApproveAcquireSchema),
  mastercardMatchProHandleManualAcquiringEntityController
);
dashboardsRoute.post(
  '/mastercard-match-pro-handle-manual-acquire-by-ref-number',
  authenticateJWT,
  validateRequest(manualApproveAcquireByRefNumberSchema),
  mastercardMatchProHandleManualAcquiringEntityByRefNumberController
);
mastercardMatchProHandleManualAcquiringEntityByRefNumberController;
dashboardsRoute.post(
  '/visa-vmss-handle-manual-acquire',
  authenticateJWT,
  validateRequest(manualApproveAcquireSchema),
  visaVmssHandleManualAcquiringEntityController
);
dashboardsRoute.get(
  '/acquiring-entity-with-all-properties/:acquiringEntityId',
  authenticateJWT,
  getAcquiringEntityWithAllPropertiesController
);
dashboardsRoute.get('/principal/get-values', authenticateJWT, getPrincipalDefaultValuesController);
dashboardsRoute.post('/acquiring-entity/create', authenticateJWT, createAcquiringEntityDashboardController);
dashboardsRoute.post(
  '/acquiring-entity/update',
  authenticateJWT,
  // validateRequest(baseAcquiringEntityUpdateSchema),
  postAcquiringEntityUpdateController
);

dashboardsRoute.post('/mid-configuration/:entrypoint', processMidConfigurationController);
dashboardsRoute.post('/:entrypoint/retry/:acquiringEntityId', retryToOnboardController);

export default dashboardsRoute;
