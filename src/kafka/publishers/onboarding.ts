import { kafkaService } from '@submodules/ryvyl-commons/services/kafkaService';
import { AcquiringEntityBeneficiaryWebSDKLinkEmail } from 'src/interfaces/email.interface';

export async function publishOnboardingEvent(key: string, data: any) {
  await kafkaService.publish('merchant-application-onboarding', key, data);
}

export async function publishAciEvent(key: string, message: any) {
  await kafkaService.publish('merchant-application-aci', key, message);
}

export async function publishMidEvent(key: string, message: any) {
  await kafkaService.publish('merchant-application-mid', key, message);
}

export async function publishAcquiringEntityBeneficiaryWebSDKLink(
  key: string,
  data: AcquiringEntityBeneficiaryWebSDKLinkEmail
) {
  await kafkaService.publish('acquiring-entity-beneficiary-web-sdk-link', key, data);
}
