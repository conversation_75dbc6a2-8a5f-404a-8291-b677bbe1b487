import { kafkaService } from '../../../submodules/ryvyl-commons/services/kafkaService';
import { AcquiringEntity } from '../../interfaces/acquiringEntityInterface';
import { baseAcquiringEntitySchema } from '../../schemaValidations/acquiringEntitySchemaValidation';
import { createAcquiringEntityWithPrincipals } from '../../services/acquiringEntityService';
import { createFailedCreatingAcquiring } from '../../services/failedCreatingAcquiringService';
import { generateAcquiringEntityPDF } from '../../services/htmlPdfGenerationService';
import { acquiringEntityLogger } from '../../utils/logger';

export async function startAcquiringEntityConsumerCreation() {
  try {
    await kafkaService.subscribe(
      ['jotform-acquiring-body-information'], // Topic name
      false, // Read from the beginning
      processAcquiringEntityMessage,
      'jotform-acquiring-body-information' // groupId additional parameter
    );
  } catch (error: any) {
    throw new Error(`❌ Error starting Kafka consumer: ${error.message}`);
  }
}

async function processAcquiringEntityMessage(_topic: string, data: AcquiringEntity) {
  try {
    // ✅ Validate message using Joi schema
    const { error, value } = baseAcquiringEntitySchema.validate(data, { abortEarly: false });

    if (error) {
      const errorMessages = error.details.map((err) => err.message).join(', ');

      throw new Error(errorMessages);
    }
    const acquiringEntity: AcquiringEntity = data;

    // Create acquiring entity with principals
    await createAcquiringEntityWithPrincipals(acquiringEntity);
    acquiringEntityLogger.info(
      `✅ Successfully processed Acquiring Entity from kafka with registration number: ${acquiringEntity.registrationNumber}`
    );
    // Generate PDF for the acquiring entity and send to Kafka
    // try {
    //   // Generate PDF as Base64
    //   const pdfData = await generateAcquiringEntityPDF(acquiringEntity);

    //   acquiringEntityLogger.info(
    //     `✅ PDF generated for Acquiring Entity with registration number: ${acquiringEntity.registrationNumber}`
    //   );

    //   // Send PDF data to Kafka topic for email processing
    //   const message = {
    //     pdfContent: pdfData.base64Content,
    //     recipientEmail: pdfData.email
    //   };

    //   await kafkaService.publish('onboarding-merchant-email', pdfData.email, message);

    //   acquiringEntityLogger.info(
    //     `✅ PDF data sent to email service for Acquiring Entity with registration number: ${acquiringEntity.registrationNumber}`
    //   );
    // } catch (pdfError: any) {
    //   // Log PDF generation/sending error but don't fail the entire process
    //   acquiringEntityLogger.error(
    //     `❌ Error processing PDF for Acquiring Entity with registration number: ${acquiringEntity.registrationNumber}: ${pdfError.message}`
    //   );
    // }
  } catch (error: any) {
    await createFailedCreatingAcquiring({
      receivedData: data,
      error: error.message
    }).catch((err: any) => {
      acquiringEntityLogger.error(`Failed to create failed creating acquiring error:${err.message}`);
    });
    acquiringEntityLogger.error(
      `❌ Error processing create acquiring entity with submissionId: ${data.submissionId} and registration number ${data.registrationNumber} from kafka: ${error.message}`
    );
  }
}
