import { kafkaService } from '@submodules/ryvyl-commons/services/kafkaService';
import logger from '@submodules/ryvyl-commons/services/loggerService';
import { AciResult, MidData, OmnipayResult } from 'src/interfaces/acquiringEntityInterface';
import {
  addAciResultToAcquiringEntity,
  addMidDataToAcquiringEntity,
  addOmnipayResultToAcquiringEntity
} from '../../services/acquiringEntityService';
import { publishAciEvent, publishOnboardingEvent } from '../publishers/onboarding';

export async function startOmnipayConsumer() {
  try {
    await kafkaService.subscribe(
      ['merchant-application-omnipay-response'],
      false,
      processOmnipayMessage,
      'merchant-application-omnipay-response'
    );
  } catch (error: any) {
    logger.error(`❌ Error starting Kafka consumer: ${error.message}`);
  }
}

export async function startAciConsumer() {
  try {
    await kafkaService.subscribe(
      ['merchant-application-aci-response'],
      false,
      processAciMessage,
      'merchant-application-aci-response'
    );
  } catch (error: any) {
    throw new Error(`❌ Error starting Kafka consumer: ${error.message}`);
  }
}

export async function startMidConsumer() {
  try {
    await kafkaService.subscribe(
      ['merchant-application-mid-response'],
      false,
      processMidMessage,
      'merchant-application-mid-response'
    );
  } catch (error: any) {
    throw new Error(`❌ Error starting Kafka consumer: ${error.message}`);
  }
}

async function processOmnipayMessage(topic: string, data: any) {
  try {
    const omnipayResult = data.result as OmnipayResult;

    const result = await addOmnipayResultToAcquiringEntity(data.acquiringEntityId, omnipayResult);

    if (!result) {
      throw new Error('Failed to add omnipay result to acquiring entity');
    }

    const message = {
      id: data.acquiringEntityId,
      result: result
    };

    if (!omnipayResult.error) await publishAciEvent(`aci-${data.acquiringEntityId}`, message);
    logger.info(`✅ Successfully processed omnipay message for acquiring entity with id: ${data.acquiringEntityId}`);
  } catch (error: any) {
    logger.error(`❌ Error processing message from topic: "${topic}", error: ${error.message}`);
  }
}

async function processAciMessage(topic: string, data: any) {
  try {
    const aciResult = data.result as AciResult;
    const result = await addAciResultToAcquiringEntity(data.acquiringEntityId, aciResult);

    if (!result) {
      throw new Error('Failed to add aci result to acquiring entity');
    }

    logger.info(`✅ Successfully processed aci message for acquiring entity with id: ${data.acquiringEntityId}`);
  } catch (error: any) {
    throw new Error(`❌ Error processing message from topic: "${topic}", error: ${error.message}`);
  }
}

async function processMidMessage(topic: string, data: any) {
  try {
    const id = data.acquiringEntityId;
    let midData: MidData;

    if (data.error) midData = data as MidData;
    else midData = data.newMid as MidData;

    const result = await addMidDataToAcquiringEntity(id, midData);

    if (!result) throw new Error('Failed to add mid data to acquiring entity');

    const message = {
      id: id,
      result: result
    };

    if (!midData.error) await publishOnboardingEvent(`onboard-${id}`, message);

    logger.info(`✅ Successfully processed mid message for acquiring entity with id: ${data.acquiringEntityId}`);
  } catch (error: any) {
    logger.error(`❌ Error processing message from topic: "${topic}", error: ${error.message}`);
  }
}
