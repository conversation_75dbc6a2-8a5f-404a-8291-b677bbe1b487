import { startAcquiringEntityConsumerCreation } from './acquiringEntityConsumer';
import { startMidConsumer } from './onboardingConsumer';

import {
  startAcquiringEntitySumsubApplicantWebhookConsumer,
  sumsubAcquiringEntityPrincipalKycCheckWebhookConsumer
} from './sumsubConsumer';
import { startOmnipayConsumer } from './onboardingConsumer';
import { startAciConsumer } from './onboardingConsumer';

export async function startKafkaConsumers() {
  await startAcquiringEntitySumsubApplicantWebhookConsumer();
  await sumsubAcquiringEntityPrincipalKycCheckWebhookConsumer();
  await startAcquiringEntityConsumerCreation();
  await startMidConsumer();
  await startOmnipayConsumer();
  await startAciConsumer();
}
