import { handleSumsubApplicantAcquiringEntityPrincipalKycCheckDataWebhook } from '../../services/principalService';
import { kafkaService } from '../../../submodules/ryvyl-commons/services/kafkaService';
import { handleSumsubApplicantAcquiringEntityDataWebhook } from '../../services/acquiringEntityService';
import logger from '@submodules/ryvyl-commons/services/loggerService';

export async function startAcquiringEntitySumsubApplicantWebhookConsumer() {
  try {
    await kafkaService.subscribe(
      ['sumsub-acquiring-entity-webhook-data'],
      false,
      handleSumsubApplicantAcquiringEntityDataWebhook,
      'sumsub-acquiring-entity-webhook-data'
    );
  } catch (error: any) {
    throw new Error(
      `❌ Failed to starting Kafka consumer "sumsub-acquiring-entity-webhook-data", error: ${error.message}`
    );
  }
}

export async function sumsubAcquiringEntityPrincipalKycCheckWebhookConsumer() {
  try {
    await kafkaService.subscribe(
      ['sumsub-acquiring-entity-principal-kyc-check-webhook-data'],
      false,
      handleSumsubApplicantAcquiringEntityPrincipalKycCheckDataWebhook,
      'sumsub-acquiring-entity-principal-kyc-check-webhook-data'
    );
  } catch (error: any) {
    throw new Error(
      `❌ Failed to starting Kafka consumer "sumsub-acquiring-entity-webhook-data", error: ${error.message}`
    );
  }
}
