import axios, { AxiosInstance, AxiosRequestConfig, Method } from 'axios';
import { RootRequestVmssMerchantMatch } from '../interfaces/visaVmssInterface';

function getOptions(
  method: Method,
  path: string = '',
  data: Record<string, any> = {},
  params: Record<string, any> = {}
) {
  const hostname = process.env.RYVYL_VISA_VMSS_SERVICE_BASE_URL;
  if (!hostname) {
    throw new Error('RYVYL_VISA_VMSS_SERVICE_BASE_URL environment variables is not defined!');
  }

  const config = {
    method: method.toUpperCase(),
    url: `${hostname}${path}`,
    headers: {
      'Content-Type': 'application/json'
    },
    ...(method.toUpperCase() !== 'GET' && { data }),
    params: params
  };

  return config;
}

export async function checkAcquiringEntityRequest(acquiringEntity: RootRequestVmssMerchantMatch) {
  const options = getOptions('POST', '/visa-vmss/merchant-match', acquiringEntity);
  const response = await axios.request(options);
  return response.data?.data;
}
