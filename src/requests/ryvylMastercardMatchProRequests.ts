import axios, { Method } from 'axios';
import { TerminationInquiryRequest } from '../interfaces/mastercardMatchProInterfaces';

function getOptions(
  method: Method,
  path: string = '',
  data: Record<string, any> = {},
  params: Record<string, any> = {}
) {
  const hostname = process.env.RYVYL_MASTERCARD_MATCH_PRO_SERVICE_BASE_URL;
  if (!hostname) {
    throw new Error('RYVYL_MASTERCARD_MATCH_PRO_SERVICE_BASE_URL environment variables is not defined!');
  }

  const config = {
    method: method.toUpperCase(),
    url: `${hostname}${path}`,
    headers: {
      'Content-Type': 'application/json'
    },
    ...(method.toUpperCase() !== 'GET' && { data }),
    params: params
  };

  return config;
}

export async function mastercardMatchProCheckAcquiringEntityRequest(
  acquiringEntity: TerminationInquiryRequest,
  page_length: number,
  page_offset: number
) {
  try {
    const options = getOptions(
      'POST',
      `/termination-inquiries?page_length=${page_length}&page_offset=${page_offset}`,
      acquiringEntity
    );
    const response = await axios.request(options);
    return response.data?.data;
  } catch (error: any) {
    handleError(error);
  }
}

export async function mastercardMatchGetRetrievedHistoryTerminationRequest(
  referenceNumber: string,
  page_length: number,
  page_offset: number
) {
  try {
    const options = getOptions(
      'GET',
      `/termination-inquiries/${referenceNumber}?page_length=${page_length}&page_offset=${page_offset}`
    );

    const response = await axios.request(options);

    return response.data?.data;
  } catch (error: any) {
    handleError(error);
  }
}

function handleError(error: any) {
  let message = 'Something went wrong!';

  if (error.response?.data?.message?.Errors) {
    message = error.response?.data?.message.Errors;
    throw new Error(JSON.stringify(message, null, 2));
  } else if (error.response?.data?.messages) {
    // return array of messages
    message = error.response?.data?.messages.join('\n ');
  } else if (error.message) {
    message = error.message;
  }
  throw new Error(message);
}
