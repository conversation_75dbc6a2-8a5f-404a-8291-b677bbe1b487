export interface TerminationInquiryRequest {
  terminationInquiryRequest: {
    acquirerId?: string;
    merchant: Merchant;
  };
}

export interface Merchant {
  name?: string;
  doingBusinessAsName?: string;
  merchantId?: string;
  subMerchantId?: string;
  address: Address;
  phoneNumber?: string | null;
  altPhoneNumber?: string;
  merchantCategory?: string;
  nationalTaxId?: string;
  countrySubdivisionTaxId?: string;
  urls?: string[];
  principals: Principal[];
  searchCriteria?: SearchCriteria;
}

export interface Address {
  addressLineOne?: string;
  addressLineTwo?: string;
  city?: string;
  isOtherCity?: string;
  countrySubdivision?: string;
  country?: string;
  postalCode?: string;
}

export interface Principal {
  firstName?: string;
  middleName?: string;
  lastName?: string;
  address: Address;
  phoneNumber?: string;
  altPhoneNumber?: string;
  email?: string;
  driversLicense?: DriversLicense;
  dateOfBirth?: string;
  nationalId?: string;
}

export interface DriversLicense {
  number?: string;
  countrySubdivision?: string;
  country?: string;
}

export interface SearchCriteria {
  minPossibleMatchCount?: string;
}

export interface TerminationInquiryResponse {
  terminationInquiryResponse: TerminationInquiryDTO;
}

export interface TerminationInquiryDTO {
  pageOffset?: number;
  ref?: string;
  transactionReferenceNumber?: string;
  possibleMerchantMatches?: PossibleMerchantMatch[];
}

export interface PossibleMerchantMatch {
  terminatedMerchants: TerminatedMerchant[];
  totalMerchantMatches?: number;
}

export interface TerminatedMerchant {
  merchant: MerchantDetails;
  merchantMatch: MerchantMatch;
}

export interface MerchantDetails {
  name: string;
  doingBusinessAsName?: string;
  merchantId?: string;
  subMerchantId?: string;
  address: Address;
  phoneNumber: string;
  altPhoneNumber?: string;
  merchantCategory: string;
  nationalTaxId?: string;
  countrySubdivisionTaxId?: string;
  dateOpened?: string; // ISO 8601 format
  dateClosed?: string; // ISO 8601 format
  reasonCodeDesc?: string;
  addedByAcquirerId?: string;
  reasonCode?: string;
  createdDate?: string; // ISO 8601 format
  comments?: string;
  merchRefNum?: string;
  urls?: string[];
  principals: Principal[];
  urlGroups?: UrlGroup[];
}

export interface UrlGroup {
  exactMatchUrls?: Url[];
  closeMatchUrls?: Url[];
  noMatchUrls?: Url[];
}

export interface Url {
  urls: string[];
}

export interface MerchantMatch {
  name?: string;
  doingBusinessAsName?: string;
  phoneNumber?: string;
  address?: string;
  altPhoneNumber?: string;
  countrySubdivisionTaxId?: string;
  nationalTaxId?: string;
  principalMatches?: PrincipalMatch[];
}

export interface PrincipalMatch {
  name?: string;
  address?: string;
  phoneNumber?: string;
  altPhoneNumber?: string;
  nationalId?: string;
  driversLicense?: string;
  email?: string;
  dateOfBirth?: string;
}

export interface TerminationInquiryErrorResponse {
  Errors: {
    Error: ErrorDetail[];
  };
}

export interface ErrorDetail {
  Source: string;
  ReasonCode: string;
  Description: string;
  Recoverable: string; // You can use `boolean` if this field is consistently meant to be true/false
  details: string;
}

export interface MastercardMatchData {
  _id?: string;
  requestMadeToCheckMerchant?: number; // How much request made to check merchant
  data?: MastercardMatchDataItem[];
  errorMessageRequest?: any;
  failedRequest?: number;
  isThereAnyMatch?: boolean;
  resolvedMastercardMatch?: boolean;
}

export interface MastercardMatchDataItem {
  _id?: string;
  url?: string;
  isMatch?: boolean;
  resolvedMastercardMatch?: boolean;
  mastercardTotalCountMatches?: number; // Count of the matches of the request
  mastercardRefNumberMatched?: string;
  errorMessage?: any;
}
