import { ObjectId } from 'mongoose';
import { PrincipalType } from '../types/principal';
import { SumsubData } from './acquiringEntityInterface';
export interface Principal {
  firstName: string;
  middleName?: string;
  lastName: string;
  email?: string;
  phone?: string;
  // Country to be full name NOT 2 or 3 letters code
  country?: string;
  state?: string;
  city?: string;
  address1?: string;
  address2?: string;
  zip?: string;
  dateOfBirth?: string;
  passportNumber?: string;
  driverLicenseNumber?: string;
  positionInCompany?: PrincipalType;
  acquiringEntityId?: ObjectId;
  sumsub?: SumsubData;
  connectBeneficiaryToCompanyError?: any;
}
