export interface RootRequestVmssMerchantMatch {
  searchTerminatedRequest: SearchTerminatedRequest;
}

export interface SearchTerminatedRequest {
  acquirerBID?: string; // 8 digits
  acquirerCountryOrRegion: string; // 2 uppercase letters
  globalSearch: boolean;
  terminatedRecordSearchCriteria: TerminatedRecordSearchCriteria;
  retroAlertIfNoMatch?: boolean;
}

export interface TerminatedRecordSearchCriteria {
  address: Address;
  businessPhoneNumbers: string[]; // 1-3 items
  category: '0' | '1' | '2' | '3' | '4' | '5';
  DBAName: string;
  principals: Principal[];
  tradeOverInternet: boolean;
  businessEmailAddress?: string;
  businessRegistrationNumber?: string;
  financialAccts?: FinancialAccount[];
  legalName?: string;
  merchantCategoryCodes?: string[]; // 0-5 items, 4-digit codes
  taxID?: string;
  webAddresses?: string[]; // 0-3 items
}

export interface Address {
  city: string;
  countryOrRegion: string; // 2 uppercase letters
  streetAddress: string;
  stateOrProvince?: string;
  zipOrPostalCode?: string;
}

export interface VisaMatchData {
  _id?: string;
  requestMadeToCheckMerchant?: number; // How much request made to check merchant
  data?: VisaMatchDataItem[];
  errorMessageRequest?: any;
  failedRequest?: number;
  isThereAnyMatch?: boolean;
  createdAt?: Date;
  resolvedVisaMatch?: boolean;
}

export interface VisaMatchDataItem {
  _id?: string;
  url?: string;
  isMatch?: boolean;
  resolvedVisaMatch?: boolean;
  visaTotalCountMatches?: number; // Count of the matches of the request
  visaResultData?: any; // Fear we are saving all the data from the response from visa
  errorMessage?: any;
  createdAt?: Date;
}

export interface Principal {
  name: string;
  businessEmailAddress?: string;
  businessPhoneNumber?: string;
  driverLicenseNumber?: string;
  passportNumber?: string;
  principalID?: string;
  residentIDOrNationalID?: string;
  SSN?: string; // format: XXX-XX-XXXX or digits
}

export interface FinancialAccount {
  financialAccountNumber?: string;
  financialInstitutionID?: string;
  intBankAccountNumber?: string; // IBAN format
}

export interface ResponseTerminatedMatchRyvylVmss {
  possibleMatches: {
    matchedRecords: Array<{
      acquirerBID: string;
      acquirerCountryOrRegion: string; // 2 letters
      acquirerName?: string;
      terminatedRecord: {
        address: {
          city: string;
          countryOrRegion: string;
          streetAddress: string;
          stateOrProvince?: string;
          zipOrPostalCode?: string;
        };
        businessPhoneNumbers: string[];
        cardAcceptorIDs: string[];
        category: '0' | '1' | '2' | '3' | '4' | '5';
        contractEndDate: string;
        contractStartDate: string;
        DBAName: string;
        incorportationStatus: '1' | '2' | '3' | '4' | '5' | '6' | '-1';
        primaryListingReason: string;
        principals: Array<{
          name: string;
          businessEmailAddress?: string;
          businessPhoneNumber?: string;
          driverLicenseNumber?: string;
          passportNumber?: string;
          principalID?: string;
          residentIDOrNationalID?: string;
          SSN?: string;
        }>;
        tradeInternationally: boolean;
        tradeOverInternet: boolean;
        acquirerAssignedMerchantID?: string;
        businessEmailAddress?: string;
        businessRegistrationNumber?: string;
        financialAccts?: Array<{
          financialAccountNumber?: string;
          financialInstitutionID?: string;
          intBankAccountNumber?: string;
        }>;
        legalName?: string;
        merchantCategoryCodes?: string[];
        paymentFacilitatorBID?: string;
        paymentFacilitatorCountryOrRegion?: string;
        secondaryListingReason?: string;
        taxID?: string;
        webAddresses?: string[];
      };
      terminatedRecordMatch: {
        addressMatch: {
          city: 'N' | 'E' | 'P';
          countryOrRegion: 'N' | 'E' | 'P';
          stateOrProvince: 'N' | 'E' | 'P';
          streetAddress: 'N' | 'E' | 'P';
          zipOrPostalCode: 'N' | 'E' | 'P';
        };
        businessEmailAddress: 'N' | 'E' | 'P';
        businessPhoneNumbers: 'N' | 'E' | 'P';
        businessRegistrationNumber: 'N' | 'E' | 'P';
        DBAName: 'N' | 'E' | 'P';
        financialAcctsMatch: Array<{
          financialAccountNumber: 'N' | 'E' | 'P';
          financialInstitutionID: 'N' | 'E' | 'P';
          intBankAccountNumber: 'N' | 'E' | 'P';
        }>;
        legalName: 'N' | 'E' | 'P';
        merchantCategoryCodes: 'N' | 'E' | 'P';
        principalsMatch: Array<{
          businessEmailAddress: 'N' | 'E' | 'P';
          businessPhoneNumber: 'N' | 'E' | 'P';
          driverLicenseNumber: 'N' | 'E' | 'P';
          name: 'N' | 'E' | 'P';
          passportNumber: 'N' | 'E' | 'P';
          principalID: 'N' | 'E' | 'P';
          residentIDOrNationalID: 'N' | 'E' | 'P';
          SSN: 'N' | 'E' | 'P';
        }>;
        taxID: 'N' | 'E' | 'P';
        tradeOverInternet: 'N' | 'E' | 'P';
        webAddresses: 'N' | 'E' | 'P';
      };
      terminatedRefID: string;
    }>;
    totalCount: number;
  };
  searchRequestRef: {
    acquirerBID: string;
    acquirerCountryOrRegion: string;
    globalSearch?: boolean;
    searchRequestRefID?: string;
    terminatedRecordSearchCriteria: {
      address: {
        city: string;
        countryOrRegion: string;
        streetAddress: string;
        stateOrProvince?: string;
        zipOrPostalCode?: string;
      };
      businessPhoneNumbers: string[];
      category: '0' | '1' | '2' | '3' | '4' | '5';
      DBAName: string;
      principals: Array<{
        name: string;
        businessEmailAddress?: string;
        businessPhoneNumber?: string;
        driverLicenseNumber?: string;
        passportNumber?: string;
        principalID?: string;
        residentIDOrNationalID?: string;
        SSN?: string;
      }>;
      tradeOverInternet: boolean;
      businessEmailAddress?: string;
      businessRegistrationNumber?: string;
      financialAccts?: Array<{
        financialAccountNumber?: string;
        financialInstitutionID?: string;
        intBankAccountNumber?: string;
      }>;
      legalName?: string;
      merchantCategoryCodes?: string[];
      taxID?: string;
      webAddresses?: string[];
    };
  };
}
