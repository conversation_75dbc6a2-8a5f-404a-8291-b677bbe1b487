import { Principal } from './principalInterface';
import { BusinessCategoryVmss } from '../enums/businessCategory';
import { VisaMatchData } from './visaVmssInterface';
import { ApplicantStatus, ReviewStatus, ReviewAnswer } from '../enums/sumsub';
import { MCCClassification } from './mccInterface';
import { MastercardMatchData } from './mastercardMatchProInterfaces';
import { AcquiringEntityEnum } from '../enums/acquringEntityEnum';

export interface DataChanged {
  field: string;
  oldValue?: any;
  newValue?: any;
}
export interface History {
  dataChanged: DataChanged[];
  changedBy?: string;
  changedAt: Date;
}

export interface AcquiringEntityQuery {
  name?: string;
  registrationNumber?: string;
  visaMatchStatus?: string[];
  mastercardMatchStatus?: string[];
  sumsubAnswer?: string[];
  sumsubStatuses?: string[];
}
export interface AcquiringEntity {
  entityType: AcquiringEntityEnum;
  pspIsoReferralAgentName?: string;
  name: string;
  registrationNumber: string;
  incorporationDate: string;
  email?: string;
  phone?: string[];
  // Country to be full name NOT 2 or 3 letters code
  country?: string;
  state?: string;
  city?: string;
  zip?: string;
  address1?: string;
  address2?: string;
  tradeOverInternet?: boolean;
  websites?: Website[];
  isAllWebsitesClassified?: boolean;
  principalsIds?: string[];
  principalRawData?: Principal[];
  businessCategoryVmss?: BusinessCategoryVmss;
  taxId?: string; // Taxpayer Identification Number (TIN) / VAT Number
  tradingName?: string;
  address1TradingAs?: string;
  address2TradingAs?: string;
  cityTradingAs?: string;
  stateTradingAs?: string;
  zipTradingAs?: string;
  countryTradingAs?: string;
  visaMatchData?: VisaMatchData[];
  tradeName?: string;
  submissionId?: string;
  sumsub?: SumsubData;
  midConfiguration?: MidData;
  omnipayResult?: OmnipayResult;
  aciResult?: AciResult;
  mastercardMatchData?: MastercardMatchData[];
  additionalData?: AdditionalData;
  correspondenceEmail?: string;
  onboardingError?: any;
  history?: History[];
}

export interface AdditionalData {
  currencies?: string[];
  settlementCurrencies?: string[];
  integrationsRequirements?: IntegrationsRequirements;
  businessOffering?: BusinessOffering;
  cryptoSettlement?: CryptoSettlement[];
  targetMarketsAndDistribution?: TargetMarketsAndDistribution[];
  bankSettlement?: BankSettlement[];
  salesMetrics?: SalesMetrics;
  shippingInfo?: ShippingInfo;
  rdrSettings?: RdrSettings;
  masterCardSettings?: MasterCardSettings;
  settlementAccountMetrics?: SettlementAccountMetrics;
  contacts?: Contacts;
  cryptoIndustry?: CryptoIndustry;
  MSBIndustry?: MSBIndustry;
  adultServices?: AdultServices;
  datingEscortData?: DatingEscortData;
  industrySpecificStatement?: IndustrySpecificStatement;
  licenses?: Licenses;
}

export interface IntegrationsRequirements {
  integrationType?: string;
  pciComplianceStatus?: string;
  thirdPartyGateway?: string | null;
  integrationOption?: string | null;
  paymentPageHostingPreference?: string;
  paymentPageURL?: string | null;
  mandatory3ds?: boolean;
}

export interface MasterCardSettings {
  masterCardEnrollmentPreference?: string;
  automaticMasterCardRefunds?: AutomaticRefunds;
}

export interface RdrSettings {
  rdrEnrollmentPreference?: string;
  automaticRdrRefunds?: AutomaticRefunds;
}

export interface AutomaticRefunds {
  option?: string;
  thresholdAmount?: number;
}

export interface ShippingInfo {
  shippingMethods?: string;
  trackingNumber?: string;
  shippingFeesAndDeliveryTimes?: string;
}

export interface SalesMetrics {
  totalMonthlySalesVolume?: string;
  expectedMonthlyVolume?: string;
  numberOfExpectedMonthlyTransactions?: string;
  averageTransactionAmount?: string;
  highestTransactionAmount?: string;
  lowestTransactionAmount?: string;
}

export interface BusinessOffering {
  industryBusinessCategory?: IndustryBusinessCategory[];
  productOrServicesDescription?: string;
}

export interface IndustryBusinessCategory {
  category?: string;
  subcategory?: string[];
}

export interface TargetMarketsAndDistribution {
  country?: string;
  volumePercentage?: string;
}

export interface CryptoSettlement {
  currency?: string;
  percentage?: string;
  walletAddress?: string;
  preferredNetwork?: string;
  cryptocurrency?: string;
}

export interface BankSettlement {
  currency?: string;
  bankCountry?: string;
  bankName?: string;
  bankIban?: string;
  bankCode?: string;
  bankAccountHolder?: string;
}

export interface MastercardRefNumberMatched {
  refNumber: string;
  dateOfCheck: Date;
}

export interface Website {
  url: string;
  isWebsiteClassified: boolean;
  mccClassification?: MCCClassification;
  sumsub?: {
    sumsubId?: string;
    status?: ApplicantStatus;
    reviewStatus?: string;
    reviewAnswer?: string;
    levelName?: string;
    createApplicantError?: string;
    AMLError?: string;
  };
  statementDescriptor?: string;
  cityField?: string;
}

export interface SumsubData {
  sumsubId?: string;
  status?: ApplicantStatus;
  reviewStatus?: ReviewStatus;
  reviewAnswer?: ReviewAnswer;
  levelName?: string;
  createApplicantError?: any;
  AMLError?: any;
  additionalErrors?: any;
}

export interface VolumeRange {
  incoming?: string;
  outgoing?: string;
}

export interface SettlementAccountMetrics {
  monthlySalesVolume?: string;
  estimatedMonthlyTransfers?: string;
  billingMethods?: string[];
  processedCreditCardsBefore?: string;
  fraudAndChargebacksRates?: string[][];
  processingAndChargebacksHistory?: any[];
  isRevenueFromBusinessActivity?: string;
  freeTrialZeroAmount?: string | null;
}

export interface Contact {
  name: string | null;
  email: string | null;
  phone: string | null;
  other: string | null;
}

export interface Contacts {
  [key: string]: Contact;
}

export interface License {
  number: string | null;
  website: string | null;
  issuingBody: string | null;
}

export interface Licenses {
  [key: string]: License;
}

export interface CryptoIndustry {
  cryptoActivityType?: string[];
  cryptocurrenciesList?: string[];
  nativeTokensSold?: boolean;
  nativeTokensTypes?: string[] | null;
  customerBase?: string[];
  conversions?: string[];
  KYCStages?: string[];
  tools?: boolean | null;
  withdrawals?: boolean;
  withdrawalsWalletTypes?: string[] | null;
  withdrawalsProcess?: string;
  risks?: boolean;
  yearsEngaging?: string;
  training?: boolean;
  pastTransactions?: string;
  professionalBackground?: string;
}

export interface MSBIndustry {
  employeesNumber?: string;
  annualTurnover?: string;
  serviceTypes?: string[];
  customerBase?: string[];
  internationalTransactionsRatio?: string;
  enhancedDueDiligence?: boolean | null;
  KYCStages?: string[];
  customerIDVerification?: string[];
  preventionSystem?: boolean | null;
  complianceEmployeesNumber?: string;
  highRiskClientsRatio?: string;
  complianceAudits?: string;
  cryptoInvolvement?: boolean | null;
  cryptoExposureDetails?: string | null;
}

export interface AdultServices {
  contentSources?: string[];
  contentConsentAgreements?: boolean | null;
  contentVerificationMethods?: string;
  contentPoviderAgeAndIDVerification?: string;
  contentReviewProcess?: string;
  liveStreamingControl?: boolean;
  liveStreamingMonitoringAndRemoval?: string;
  removalAppealProcess?: string;
  marketingAndSearchTerms?: string;
  potentiallyIllegalContentPreventionProcess?: string;
  potentiallyIllegalContentComplaintProcess?: boolean;
  illegalContentReporting?: boolean;
  traffickingAndAbusePolicy?: string;
  consentAgreementsRetention?: boolean;
  offerAIContent?: boolean | null;
  AIContentSources?: string | null;
  AIContentAgreements?: string | null;
  confirmNoAIDeepfakes?: boolean | null;
}

export interface DatingEscortData {
  antiTraffickingProgram?: string;
  contentReviewProcess?: string;
  contentRemovalAppeals?: string;
  marketingAndSearchTermsCompliance?: string;
  contentProviderConsentsDepiction?: string;
  consentPublicDistribution?: string;
  consentContentDownload?: string;
  consentsValidationAndStorage?: string;
  evidenceValidationAndRetention?: string;
  marketingStrategy?: string;
  fakeBotPrevention?: string;
}

export interface IndustrySpecificStatement {
  highValueKYC?: boolean;
  insurance?: boolean;
  highValuePlayerKYC?: boolean;
  payoutReserves?: boolean;
  bonusAbusePrevention?: boolean;
  selfExclusionOrResponsibleTools?: boolean;
  coolingOff?: boolean;
  highValueGoods?: boolean;
  gamblingInclusion?: boolean;
  childProtection?: boolean;
  ageVerification?: boolean;
  guestOrAnnonymousAccounts?: boolean;
  spendingLimits?: boolean;
  tieredAccountSystem?: boolean;
  kycStages?: string;
  forexLicenseHolder?: string;
  gamblingLicense?: string;
  geofencing?: string;
  fraudPrevention?: string;
  minimumAgeRequirements?: string;
  payoutWithdrawals?: string;
  cardRevenueRatio?: string;
  suspiciousPatternsPrevention?: string;
  massBettingPrevention?: string;
  singleOrMultiVendor?: string;
  transacitonProcessing?: string;
  disputeResolution?: string;
  vendorsNumber?: string;
  suspiciousAccountHandling?: string;
  payoutReserveManagement?: string | null;
  selfExclusionOrResponsibleToolsDescription?: string | null;
  consumerProtectionMethods?: string[];
  marketplaceTypes?: string[];
  vendorOnboarding?: string[];
  gamingServiceTypes?: string[];
  currencyTypes?: string[];
  transactionTypes?: string[];
  accountCreationInformation?: string[];
  fraudPreventionGmg?: string[];
  digWalTypes?: string[];
  serviceTypesDigWal?: string[];
  targetAudience?: string[];
  onboardingInformation?: string[];
  IDVerification?: string[];
}

export interface MidData extends Document {
  longTerminal: string;
  parentLongTerminal: string;
  nextLongTerminal?: string;
  isMasterMid: boolean;
  error?: string;
}

export interface OmnipayResult extends Document {
  longTerminal: string;
  isMasterMid: boolean;
  childLongTerminals: {
    longTerminal: string;
    currency: string;
  }[];
  error?: string;
}

export interface AciResult extends Document {
  onboardedEntities: {
    longTerminal: string;
    currency: string;
  }[];
  error?: string;
}
