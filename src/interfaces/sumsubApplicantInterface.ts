export interface ExtendedApplicantRequest extends CreateApplicantRequest {
  levelName: string;
}

export interface CreateApplicantRequest {
  externalUserId: string; // required
  info?: {
    companyInfo?: CompanyInfo;
  };
  sourceKey?: string;
  email?: string;
  phone?: string;
  lang?: string;
  metadata?: { key: string; value: string }[];
  fixedInfo?: FixedInfo;
  type?: 'company' | 'individual';
}

export interface FixedInfo {
  companyInfo?: CompanyInfo;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  legalName?: string;
  gender?: 'M' | 'F';
  dob?: string; // YYYY-MM-DD
  placeOfBirth?: string;
  countryOfBirth?: string; // Alpha-3 code and need to be in Uppercase
  stateOfBirth?: string;
  country?: string; // Alpha-3 code and need to be in Uppercase
  nationality?: string; // Alpha-3 code and need to be in Uppercase
  addresses?: Address[];
  tin?: string;
  taxResidenceCountry?: string; // Alpha-3 code and need to be in Uppercase
  type?: string;
}

export interface CompanyInfo {
  companyName?: string;
  registrationNumber?: string;
  incorporationDate?: string;
  country?: string; // required, Alpha-3 code and need to be in Uppercase
  alternativeNames?: string[];
  legalAddress?: string;
  address?: CompanyAddress;
  incorporatedOn?: string; // YYYY-MM-DD
  type?: string;
  email?: string;
  phone?: string;
  controlScheme?: string;
  taxId?: string;
  registrationLocation?: string;
  website?: string;
  postalAddress?: string;
  noUBOs?: boolean;
  noShareholders?: boolean;
}

export interface CompanyAddress {
  street?: string;
  subStreet?: string;
  town?: string;
  state?: string;
  postCode?: string;
  country?: string; // Alpha-3 code and need to be in Uppercase
}

export interface Address {
  country?: string; // Alpha-3 code and need to be in Uppercase
  postCode?: string;
  town?: string;
  street?: string;
  subStreet?: string;
  state?: string;
  buildingName?: string;
  flatNumber?: string;
  buildingNumber?: string;
  formattedAddress?: string;
}

export type WebhookApplicant = {
  applicantId: string;
  inspectionId: string;
  applicantType: string;
  correlationId: string;
  levelName: string;
  sandboxMode?: boolean;
  externalUserId: string;
  type: string;
  reviewResult: {
    moderationComment?: string;
    clientComment?: string;
    reviewAnswer: 'RED' | 'GREEN';
    rejectLabels?: string;
    reviewRejectType?: string;
    buttonIds?: Array<string>;
  };
  reviewStatus: string;
  createdAt: string;
  createdAtMs: string;
  clientId: string;
};

export interface GenerateExternalWebSDKLinkRequest {
  levelName: string;
  userId: string;
  externalActionId?: string;
  applicantIdentifiers?: {
    email?: string;
    phone?: string;
  };
}

export interface GenerateExternalWebSDKLinkResponse {
  data: {
    url: string;
  };
}

export interface LinkBeneficiaryToCompany {
  companyApplicantId: string;
  beneficiaryData: {
    applicantId?: string;
    types: ('ubo' | 'shareholder' | 'representative' | 'director')[];
    shareSize?: string;
    beneficiaryInfo?: LinkBeneficiaryInfoData;
  };
  metadata?: MetadataItem[];
}

export interface LinkBeneficiaryInfoData {
  // Personal info
  email?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  middleName?: string;
  dob?: string; // Format: YYYY-MM-DD
  countryOfBirth?: string; // Alpha-3 code
  stateOfBirth?: string;
  placeOfBirth?: string;
  legalName?: string; // Not validated, used for navigation
  gender?: 'M' | 'F';
  nationality?: string; // Alpha-3 code
  country?: string; // Alpha-3 code
  tin?: string;
  taxResidenceCountry?: string; // Alpha-3 code

  // Company-related info (for company applicants only)
  companyName?: string;
  registrationNumber?: string;
  registrationLocation?: string;
  type?: string;
  incorporatedOn?: string; // Format: YYYY-MM-DD
  legalAddress?: string;
  postalAddress?: string;
  controlScheme?: string;
  taxId?: string;
  website?: string;

  // Address details
  street?: string;
  subStreet?: string;
  buildingNumber?: string;
  flatNumber?: string;
  town?: string;
  state?: string;
  postCode?: string;

  // System-level info
  externalUserId?: string;
}

export interface MetadataItem {
  key: string;
  value: string;
}

export interface LinkBeneficiaryResponse {
  id: string;
  applicantId?: string;
  applicant?: any; // this is applicant object
  shareSize?: number;
  types: Array<'ubo' | 'shareholder' | 'representative' | 'director'>;
  beneficiaryInfo?: LinkBeneficiaryInfoData;
  metadata?: Array<MetadataItem>;
}
