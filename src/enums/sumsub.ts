export enum ApplicantStatus {
  Approved = 'approved',
  Rejected = 'rejected',
  OnHold = 'onHold',
  Unknown = 'unknown',
  Init = 'init',
  Pending = 'pending',
  Prechecked = 'prechecked',
  Queued = 'queued',
  Completed = 'completed',
  NoStatus = 'no status'
}

export enum ReviewAnswer {
  Green = 'GREEN',
  Red = 'RED'
}

export enum ReviewStatus {
  Init = 'init',
  Pending = 'pending',
  Prechecked = 'prechecked',
  Queued = 'queued',
  Completed = 'completed',
  OnHold = 'onHold'
}
