// export function flattenObject(obj: any, prefix = ''): Record<string, string> {
//   let result: Record<string, string> = {};

//   for (let key in obj) {
//     if (obj.hasOwnProperty(key)) {
//       let newKey = prefix ? `${prefix}-${key}` : key; // Keep key hierarchy

//       if (Array.isArray(obj[key])) {
//         // If it's an array, handle elements separately
//         obj[key].forEach((item, index) => {
//           if (typeof item === 'object' && item !== null) {
//             Object.assign(result, flattenObject(item, `${newKey}[${index}]`));
//           } else {
//             result[`${newKey}[${index}]`] = item;
//           }
//         });
//       } else if (typeof obj[key] === 'object' && obj[key] !== null) {
//         // If it's a nested object, recurse
//         Object.assign(result, flattenObject(obj[key], newKey));
//       } else {
//         // Otherwise, just add the key-value pair
//         result[newKey] = obj[key];
//       }

//     }
//   }

//   return result;
// }
export function flattenObject(
  obj: any,
  prefix = '',
  keysToExclude: string[] = [] // Keys to keep as-is
): Record<string, any> {
  let result: Record<string, any> = {};

  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      let newKey = prefix ? `${prefix}-${key}` : key;

      if (keysToExclude.includes(key)) {
        // Store the array or object as-is without flattening
        result[newKey] = obj[key];
      } else if (Array.isArray(obj[key])) {
        // If it's an array, handle elements separately
        obj[key].forEach((item, index) => {
          if (typeof item === 'object' && item !== null) {
            Object.assign(result, flattenObject(item, `${newKey}[${index}]`, keysToExclude));
          } else {
            result[`${newKey}[${index}]`] = item;
          }
        });
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        // If it's a nested object, recurse
        Object.assign(result, flattenObject(obj[key], newKey, keysToExclude));
      } else {
        // Otherwise, just add the key-value pair
        result[newKey] = obj[key];
      }
    }
  }

  return result;
}

export function getUniqueKeys(arrayOfObjects: any, excludeKeys: string[] = []) {
  const uniqueKeys = new Set(); // Use a Set to store unique keys

  arrayOfObjects.forEach((obj: any) => {
    Object.keys(obj).forEach((key) => {
      if (!excludeKeys.includes(key)) {
        uniqueKeys.add(key); // Add each key to the Set
      }
    });
  });

  return Array.from(uniqueKeys); // Convert Set to an array
}

export function sortKeys(keysArray: string[], customOrder: string[]) {
  return keysArray.sort((a, b) => {
    let indexA = customOrder.indexOf(a);
    let indexB = customOrder.indexOf(b);

    // If a key is not in customOrder, place it at the end
    indexA = indexA === -1 ? customOrder.length : indexA;
    indexB = indexB === -1 ? customOrder.length : indexB;

    return indexA - indexB;
  });
}

export function transformSpecialKeys(key: string, value: any[]): Record<string, any> {
  let transformedResult: Record<string, any> = {};

  if (key === 'urls') {
    transformedResult[key] = {};
    value.forEach((item, index) => {
      transformedResult[key][index + 1] = item; // Convert array to object with numeric keys
    });
  } else if (key === 'principals') {
    value.forEach((item, index) => {
      transformedResult[`principals${index + 1}`] = item; // Convert array elements to separate objects
    });
  }

  return transformedResult;
}
