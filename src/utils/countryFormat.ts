import countries from 'i18n-iso-countries';

export function getTwoLetterCode(threeLetterCode: string) {
  return countries.toAlpha2(threeLetterCode);
}

export function getTwoLetterCodeFromFullName(countryName: string): string | undefined {
  // Convert the full country name to its two-letter code
  return countries.getAlpha2Code(countryName, 'en');
}

export function getThreeLetterCodeFromFullName(countryName: string): string | undefined {
  // Convert the full country name to its two-letter code
  return countries.getAlpha3Code(countryName, 'en');
}

export function getFullNameFromThreeLetterCode(threeLetterCode: string): string | undefined {
  return countries.getName(threeLetterCode, 'en');
}
