import { Response } from 'express';

import { CustomError } from '../classes/CustomError';

function handleCustomError(error: any, res: Response) {
  if (error instanceof CustomError) {
    const status = error.statusCode ?? 400;
    const message = error.message ? error.message : 'Something went wrong, please try again!';
    return res.status(status).send({
      message: message
    });
  }

  return res.status(500).json({ message: 'Something went wrong, please try again!' });
}

export default handleCustomError;
