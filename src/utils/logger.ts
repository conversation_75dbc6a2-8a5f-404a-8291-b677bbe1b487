import { createLoggerInstance } from '@submodules/ryvyl-commons/services/loggerService';
import logger from '@submodules/ryvyl-commons/services/loggerService';

export const acquiringEntityLogger = createLoggerInstance('Acquiring-Entity');
export const visaVmssLogger = createLoggerInstance('Visa-Vmss');
export const mastercardLogger = createLoggerInstance('Mastercard');
export const sumsubLogger = createLoggerInstance('Sumsub');
export const onboardingLogger = createLoggerInstance('Onboarding');
export const mccClassificationLogger = createLoggerInstance('Mcc-Classification');
export const principalLogger = createLoggerInstance('Principal');

logger.info('Logger attached.');

export default logger;
