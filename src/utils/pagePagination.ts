export function getPaginationData(
  totalDocs: number,
  page_length: number,
  page_offset: number,
  hardcoreTotalPages?: number
) {
  const totalPages = hardcoreTotalPages ? hardcoreTotalPages : Math.ceil(totalDocs / page_length);

  const hasNextPage = page_offset < totalPages;
  const hasPrevPage = page_offset > 1;

  const returnData = {
    hasNextPage,
    hasPrevPage,
    limit: page_length,
    nextPage: hasNextPage ? page_offset + 1 : null,
    page: page_offset,
    pagingCounter: page_offset,
    prevPage: hasPrevPage ? page_offset - 1 : null,
    totalDocs: totalDocs,
    totalPages: totalPages
  };
  return returnData;
}
