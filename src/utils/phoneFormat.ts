import { parsePhoneNumberFromString } from 'libphonenumber-js';

function getLocalPhoneNumberWithCountryCode(phoneNumber: string): string | null {
  if (!phoneNumber.startsWith('+')) {
    return phoneNumber.replace(/\D/g, ''); // remove any non-digit characters
  }

  const parsedNumber = parsePhoneNumberFromString(phoneNumber);

  if (!parsedNumber) return null;

  // For US numbers, return 10-digit national number
  if (parsedNumber.country === 'US') {
    return parsedNumber.nationalNumber; // This is always numeric
  } else {
    const parsedNumber = parsePhoneNumberFromString(phoneNumber);
    if (!parsedNumber) return null;

    const localNumber = parsedNumber.formatNational(); // Returns correct local format
    // Remove spaces
    const cleanedPhone = localNumber.replace(/\s+/g, '');
    return cleanedPhone;
  }
}

export { getLocalPhoneNumberWithCountryCode };
