import {
  AciResult,
  AcquiringEntity,
  DataChanged,
  MidData,
  OmnipayResult,
  Website
} from '../interfaces/acquiringEntityInterface';
import AcquiringEntityModel, { AcquiringEntityModelDocument } from '../models/mongo/acquiringEntity';
import {
  createPrincipal,
  createSumsubApplicantForPrincipal,
  deletePrincipal,
  getPrincipalById,
  getPrincipalDefaultValues,
  getSumsubApplicantWebSDKLinkForPrincipal,
  updatePrincipal
} from './principalService';
import principal, { PrincipalModelDocument } from '../models/mongo/principal';
import { Principal } from '../interfaces/principalInterface';
import {
  acquiringEntityLogger,
  mastercardLogger,
  mccClassificationLogger,
  onboardingLogger,
  sumsubLogger,
  visaVmssLogger
} from '../utils/logger';
import { get, ObjectId, PaginateOptions, QueryOptions } from 'mongoose';
import { CustomError } from '../classes/CustomError';
import { vmssAcquiringEntityMatchWithDescriptionInformation } from './visaVmssService';
import { ResponseTerminatedMatchRyvylVmss, VisaMatchData, VisaMatchDataItem } from '../interfaces/visaVmssInterface';
import { acquiringEntityMatchPro } from './mastercardService';
import {
  ManualApproveAcquire,
  ManualApproveAcquireByRefNumber
} from '../interfaces/acquiringEntityBodyRequestInterface';
import { BusinessCategoryVmss } from '../enums/businessCategory';
import safeJsonParse from '../utils/jsonParse';

import { getThreeLetterCodeFromFullName } from '../utils/countryFormat';
import { SUMSUB } from '../config/config';
import {
  createApplicant,
  generateExternalWebSDKLink,
  linkBeneficiaryToCompany,
  rumAMLApplicantCheck
} from './sumsubService';
import {
  LinkBeneficiaryResponse,
  LinkBeneficiaryToCompany,
  WebhookApplicant
} from '../interfaces/sumsubApplicantInterface';
import HttpException from '@submodules/ryvyl-commons/classes/HttpException';
import { getMccClassification } from './mccClassificationServer';
import { MCCClassification } from '../interfaces/mccInterface';
import {
  MastercardMatchData,
  MastercardMatchDataItem,
  TerminationInquiryDTO
} from '../interfaces/mastercardMatchProInterfaces';
import { getPaginationData } from '../utils/pagePagination';
import { sendForOnboarding } from './onboarding';
import { publishAcquiringEntityBeneficiaryWebSDKLink } from '../kafka/publishers/onboarding';
import { AcquiringEntityBeneficiaryWebSDKLinkEmail } from '../interfaces/email.interface';
import { ReviewAnswer, ReviewStatus } from '../enums/sumsub';

export async function createAcquiringEntity(data: AcquiringEntity) {
  const acquiringEntity = await AcquiringEntityModel.create(data);
  return acquiringEntity;
}

export async function getAcquiringEntitiesService(query: any, options: PaginateOptions) {
  const acquiringEntities = await AcquiringEntityModel.paginate(query, options);
  return acquiringEntities;
}

export async function getAcquiringEntitiesWithFilters(query: any, options: PaginateOptions) {
  // Build the MongoDB query based on the provided filters
  const mongoQuery: any = {};

  // Search by name (case-insensitive)
  if (query.name) {
    mongoQuery.name = { $regex: new RegExp(query.name, 'i') };
  }

  // Search by registration number (case-insensitive)
  if (query.registrationNumber) {
    mongoQuery.registrationNumber = { $regex: new RegExp(query.registrationNumber, 'i') };
  }

  // Filter by Visa match status
  if (query.visaMatchStatus) {
    if (!mongoQuery.$and) {
      mongoQuery.$and = [];
    }

    const visaStatuses = Array.isArray(query.visaMatchStatus) ? query.visaMatchStatus : [query.visaMatchStatus];
    const visaConditions: any[] = [];

    for (const status of visaStatuses) {
      switch (status) {
        case 'In Progress':
          visaConditions.push({
            $expr: {
              $or: [
                { $eq: [{ $ifNull: ['$visaMatchData', []] }, []] },
                { $eq: [{ $size: { $ifNull: ['$visaMatchData', []] } }, 0] }
              ]
            }
          });
          break;
        case 'Failed Requests':
          visaConditions.push({
            $expr: {
              $gt: [{ $arrayElemAt: ['$visaMatchData.failedRequest', -1] }, 0]
            }
          });
          break;
        case 'Resolved (Approved)':
          visaConditions.push({
            $expr: {
              $and: [
                { $eq: [{ $arrayElemAt: ['$visaMatchData.resolvedVisaMatch', -1] }, true] },
                { $eq: [{ $arrayElemAt: ['$visaMatchData.failedRequest', -1] }, 0] },
                { $eq: [{ $arrayElemAt: ['$visaMatchData.isThereAnyMatch', -1] }, true] }
              ]
            }
          });
          break;
        case 'Resolved (Rejected)':
          visaConditions.push({
            $expr: {
              $and: [
                { $eq: [{ $arrayElemAt: ['$visaMatchData.resolvedVisaMatch', -1] }, false] },
                { $eq: [{ $arrayElemAt: ['$visaMatchData.failedRequest', -1] }, 0] },
                { $eq: [{ $arrayElemAt: ['$visaMatchData.isThereAnyMatch', -1] }, false] }
              ]
            }
          });
          break;
        case 'Match':
          visaConditions.push({
            $expr: {
              $and: [
                { $eq: [{ $arrayElemAt: ['$visaMatchData.isThereAnyMatch', -1] }, true] },
                {
                  $or: [
                    { $not: [{ $ifNull: [{ $arrayElemAt: ['$visaMatchData.resolvedMastercardMatch', -1] }, false] }] },
                    { $eq: [{ $arrayElemAt: ['$visaMatchData.resolvedMastercardMatch', -1] }, null] }
                  ]
                }
              ]
            }
          });
          break;
        case 'No Match':
          visaConditions.push({
            $expr: {
              $and: [
                { $eq: [{ $arrayElemAt: ['$visaMatchData.isThereAnyMatch', -1] }, false] },
                { $eq: [{ $arrayElemAt: ['$visaMatchData.failedRequest', -1] }, 0] }
              ]
            }
          });
          break;
        case 'Error':
          visaConditions.push({
            $expr: {
              $and: [
                { $gt: [{ $size: { $ifNull: ['$visaMatchData', []] } }, 0] },
                { $not: [{ $ifNull: [{ $arrayElemAt: ['$visaMatchData.requestMadeToCheckMerchant', -1] }, false] }] },
                { $not: [{ $ifNull: [{ $arrayElemAt: ['$visaMatchData.failedRequest', -1] }, false] }] }
              ]
            }
          });
          break;
      }
    }

    if (visaConditions.length > 0) {
      mongoQuery.$and.push(visaConditions.length === 1 ? visaConditions[0] : { $or: visaConditions });
    }
  }

  // Filter by Mastercard match status
  if (query.mastercardMatchStatus) {
    if (!mongoQuery.$and) {
      mongoQuery.$and = [];
    }

    const mastercardStatuses = Array.isArray(query.mastercardMatchStatus)
      ? query.mastercardMatchStatus
      : [query.mastercardMatchStatus];
    const mastercardConditions: any[] = [];

    for (const status of mastercardStatuses) {
      switch (status) {
        case 'In Progress':
          mastercardConditions.push({
            $expr: {
              $or: [
                { $eq: [{ $ifNull: ['$mastercardMatchData', []] }, []] },
                { $eq: [{ $size: { $ifNull: ['$mastercardMatchData', []] } }, 0] }
              ]
            }
          });
          break;
        case 'Failed Requests':
          mastercardConditions.push({
            $expr: {
              $gt: [{ $arrayElemAt: ['$mastercardMatchData.failedRequest', -1] }, 0]
            }
          });
          break;
        case 'Resolved (Approved)':
          mastercardConditions.push({
            $expr: {
              $and: [
                { $gt: [{ $size: '$mastercardMatchData' }, 0] },
                { $eq: [{ $arrayElemAt: ['$mastercardMatchData.resolvedMastercardMatch', -1] }, true] },
                { $eq: [{ $arrayElemAt: ['$mastercardMatchData.failedRequest', -1] }, 0] },
                { $eq: [{ $arrayElemAt: ['$mastercardMatchData.isThereAnyMatch', -1] }, true] }
              ]
            }
          });
          break;
        case 'Resolved (Rejected)':
          mastercardConditions.push({
            $expr: {
              $and: [
                { $eq: [{ $arrayElemAt: ['$mastercardMatchData.resolvedMastercardMatch', -1] }, false] },
                { $eq: [{ $arrayElemAt: ['$mastercardMatchData.failedRequest', -1] }, 0] },
                { $eq: [{ $arrayElemAt: ['$mastercardMatchData.isThereAnyMatch', -1] }, false] }
              ]
            }
          });
          break;
        case 'Match':
          mastercardConditions.push({
            $expr: {
              $and: [
                { $eq: [{ $arrayElemAt: ['$mastercardMatchData.isThereAnyMatch', -1] }, true] },
                {
                  $or: [
                    {
                      $not: [
                        { $ifNull: [{ $arrayElemAt: ['$mastercardMatchData.resolvedMastercardMatch', -1] }, false] }
                      ]
                    },
                    { $eq: [{ $arrayElemAt: ['$mastercardMatchData.resolvedMastercardMatch', -1] }, null] }
                  ]
                }
              ]
            }
          });
          break;
        case 'No Match':
          mastercardConditions.push({
            $expr: {
              $and: [
                { $eq: [{ $arrayElemAt: ['$mastercardMatchData.isThereAnyMatch', -1] }, false] },
                { $eq: [{ $arrayElemAt: ['$mastercardMatchData.failedRequest', -1] }, 0] }
              ]
            }
          });
          break;
        case 'Error':
          mastercardConditions.push({
            $expr: {
              $and: [
                { $gt: [{ $size: { $ifNull: ['$mastercardMatchData', []] } }, 0] },
                {
                  $not: [
                    { $ifNull: [{ $arrayElemAt: ['$mastercardMatchData.requestMadeToCheckMerchant', -1] }, false] }
                  ]
                },
                { $not: [{ $ifNull: [{ $arrayElemAt: ['$mastercardMatchData.failedRequest', -1] }, false] }] }
              ]
            }
          });
          break;
      }
    }

    if (mastercardConditions.length > 0) {
      mongoQuery.$and.push(mastercardConditions.length === 1 ? mastercardConditions[0] : { $or: mastercardConditions });
    }
  }

  // Filter by SumSub answer
  if (query.sumsubAnswer) {
    const sumsubAnswers = Array.isArray(query.sumsubAnswer) ? query.sumsubAnswer : [query.sumsubAnswer];
    const noAnswerIndex = sumsubAnswers.indexOf('No Answer');

    if (noAnswerIndex !== -1) {
      // Remove "No Answer" from the array
      const otherAnswers = [...sumsubAnswers];
      otherAnswers.splice(noAnswerIndex, 1);

      if (otherAnswers.length > 0) {
        // If there are other answers, use $or to combine with undefined reviewAnswer
        mongoQuery.$or = [
          { 'sumsub.reviewAnswer': { $in: otherAnswers } },
          { 'sumsub.reviewAnswer': { $exists: false } },
          { 'sumsub.reviewAnswer': null }
        ];
      } else {
        // If "No Answer" is the only selected answer, just query for undefined reviewAnswer
        mongoQuery.$or = [{ 'sumsub.reviewAnswer': { $exists: false } }, { 'sumsub.reviewAnswer': null }];
      }
    } else {
      // If "No Answer" is not selected, just use the regular $in query
      if (sumsubAnswers.length === 1) {
        mongoQuery['sumsub.reviewAnswer'] = sumsubAnswers[0];
      } else {
        mongoQuery['sumsub.reviewAnswer'] = { $in: sumsubAnswers };
      }
    }
  }

  // Filter by SumSub statuses (multiple statuses can be selected)
  if (query.sumsubStatuses && query.sumsubStatuses.length > 0) {
    const noStatusIndex = query.sumsubStatuses.indexOf('no status');

    if (noStatusIndex !== -1) {
      // Remove "no status" from the array
      const otherStatuses = [...query.sumsubStatuses];
      otherStatuses.splice(noStatusIndex, 1);

      if (otherStatuses.length > 0) {
        // If there are other statuses, use $or to combine with undefined reviewStatus
        mongoQuery.$or = [
          { 'sumsub.reviewStatus': { $in: otherStatuses } },
          { 'sumsub.reviewStatus': { $exists: false } },
          { 'sumsub.reviewStatus': null }
        ];
      } else {
        // If "no status" is the only selected status, just query for undefined reviewStatus
        mongoQuery.$or = [{ 'sumsub.reviewStatus': { $exists: false } }, { 'sumsub.reviewStatus': null }];
      }
    } else {
      // If "no status" is not selected, just use the regular $in query
      mongoQuery['sumsub.reviewStatus'] = { $in: query.sumsubStatuses };
    }
  }

  const acquiringEntities = await AcquiringEntityModel.paginate(mongoQuery, options);
  return acquiringEntities;
}

export async function getAcquiringEntityById(id: string): Promise<AcquiringEntityModelDocument | null> {
  const acquiringEntity = await AcquiringEntityModel.findById(id).populate('principalsIds');
  return acquiringEntity;
}

export async function checkAcquiringEntityByRegistrationNumber(
  registrationNumber: string
): Promise<AcquiringEntity | null> {
  const existingEntity = await AcquiringEntityModel.findOne({ registrationNumber });
  return existingEntity;
}

export async function entitiesWithMissingMcc() {
  const acquiringEntities = await AcquiringEntityModel.find({
    websites: {
      $elemMatch: {
        url: { $exists: true, $ne: null },
        $or: [
          { mccClassification: { $exists: false } },
          { 'mccClassification.mcc': { $exists: false } },
          { 'mccClassification.mcc': null }
        ],
        'mccClassification.retryCount': { $lt: 3 }
      }
    }
  });
  return acquiringEntities;
}

export async function retryMccClassification() {
  try {
    const acquiringEntities = await entitiesWithMissingMcc();
    for (const acquiringEntity of acquiringEntities) {
      await processAcquiringEntityScreening((acquiringEntity._id as ObjectId).toString(), false);
    }
  } catch (error: any) {
    acquiringEntityLogger.error(`Failed to retry mcc classification with error: ${error.message}`);
  }
}

export async function addMidDataToAcquiringEntity(
  id: string,
  midData: MidData
): Promise<AcquiringEntityModelDocument | null> {
  const result = await AcquiringEntityModel.findByIdAndUpdate(
    id,
    {
      $set: {
        midConfiguration: midData
      }
    },
    { new: true, runValidators: true }
  );

  return result;
}

export async function clearMidConfiguration(acquiringEntityId: string) {
  return await AcquiringEntityModel.findByIdAndUpdate(
    acquiringEntityId,
    {
      $set: {
        midConfiguration: null
      }
    },
    { new: true }
  );
}

export async function addOmnipayResultToAcquiringEntity(
  id: string,
  omnipayResult: OmnipayResult
): Promise<AcquiringEntityModelDocument | null> {
  const result = await AcquiringEntityModel.findByIdAndUpdate(
    id,
    {
      $set: {
        omnipayResult: omnipayResult
      }
    },
    { new: true, runValidators: true }
  );

  return result;
}

export async function addAciResultToAcquiringEntity(
  id: string,
  aciResult: AciResult
): Promise<AcquiringEntityModelDocument | null> {
  const result = await AcquiringEntityModel.findByIdAndUpdate(
    id,
    {
      $set: {
        aciResult: aciResult
      }
    },
    { new: true, runValidators: true }
  );

  return result;
}

export async function createAcquiringEntityWithPrincipals(
  acquiringEntityData: AcquiringEntity
): Promise<{ acquiringEntity: any; principals: any[] }> {
  const createdPrincipals: any[] = [];

  try {
    const companyByRegistrationNumber = await checkAcquiringEntityByRegistrationNumber(
      acquiringEntityData.registrationNumber
    );
    if (companyByRegistrationNumber) {
      throw new CustomError(
        `Company with this registration number: ${acquiringEntityData.registrationNumber} already exists`,
        409
      );
    }
  } catch (error: any) {
    throw error;
  }

  try {
    // Create all principals first
    if (acquiringEntityData.principalRawData && acquiringEntityData.principalRawData.length > 0) {
      for (const principalData of acquiringEntityData.principalRawData) {
        const principal = await createPrincipal(principalData as Principal);
        createdPrincipals.push(principal);
      }

      // Update acquiring entity data with created principal IDs
      acquiringEntityData.principalsIds = createdPrincipals.map((principal) => principal._id);
    }

    // Create acquiring entity
    const acquiringEntity = await createAcquiringEntity(acquiringEntityData);

    for (const principal of createdPrincipals) {
      principal.acquiringEntityId = acquiringEntity._id;
      await principal.save();
    }

    // We do not need to wait for the screening to finish, because screening is running in background
    processAcquiringEntityScreening((acquiringEntity._id as ObjectId).toString());

    return {
      acquiringEntity,
      principals: createdPrincipals
    };
  } catch (error: any) {
    acquiringEntityLogger.error(`Failed to create acquiring entity with principals: ${error.message}`);
    // If any error occurs, delete all created principals
    for (const principal of createdPrincipals) {
      await deletePrincipal(principal._id).catch((error: any) => {
        acquiringEntityLogger.error(`Failed to delete principal with id :${principal._id}, error: ${error}`);
      });
    }

    throw error;
  }
}

// setTimeout(() => {
//   checkAcquiringEntityMatchInVmss('67ff6fa7dc5c237d7a9b9161').catch((error: any) => {});
// }, 3000);

export async function checkAcquiringEntityMatchInVmss(acquiringEntityId: string) {
  // : Promise<ResponseTerminatedMatchRyvylVmss[]>
  let acquiringEntity: AcquiringEntityModelDocument | null = null;

  acquiringEntity = await getAcquiringEntityById(acquiringEntityId);

  if (!acquiringEntity) {
    throw new CustomError('Acquiring entity with id: ${acquiringEntityId} not found', 404);
  }
  try {
    let visaVmssResponses: {
      url: string | undefined;
      error?: any;
      response?: ResponseTerminatedMatchRyvylVmss;
    }[] = [];

    visaVmssResponses = await vmssAcquiringEntityMatchWithDescriptionInformation(acquiringEntity);
    const visaMatchDataItems: VisaMatchDataItem[] = [];
    let successRequest = 0;

    for (const visaVmssResponse of visaVmssResponses) {
      try {
        if (visaVmssResponse.error) {
          throw new CustomError(visaVmssResponse.error, 400);
        }
        const totalCountMatches = visaVmssResponse?.response?.possibleMatches?.totalCount;
        if (totalCountMatches == null || isNaN(totalCountMatches)) {
          throw new Error('Invalid totalCountMatches: Value is null, undefined, or not a number.');
        }
        const isMatch = totalCountMatches > 0;
        const visaMatchDataItem: VisaMatchDataItem = {
          url: visaVmssResponse.url,
          isMatch: isMatch,
          resolvedVisaMatch: false,
          visaTotalCountMatches: totalCountMatches,
          visaResultData: visaVmssResponse.response
        };
        visaMatchDataItems.push(visaMatchDataItem);
        successRequest++;
      } catch (error: any) {
        visaMatchDataItems.push({
          errorMessage: error.message
        });
      }
    }

    let isThereAnyMatch = false;
    visaMatchDataItems.forEach((item: VisaMatchDataItem) => {
      if (item.isMatch) {
        isThereAnyMatch = true;
      }
    });

    const visaMatchData: VisaMatchData = {
      requestMadeToCheckMerchant: visaVmssResponses.length,
      data: visaMatchDataItems,
      failedRequest: visaVmssResponses.length - successRequest,
      isThereAnyMatch: isThereAnyMatch
    };

    if (acquiringEntity.visaMatchData) {
      acquiringEntity.visaMatchData.push(visaMatchData);
    } else {
      acquiringEntity.visaMatchData = [visaMatchData];
    }

    await acquiringEntity.save();
    visaVmssLogger.info(
      `Acquiring entity with name: ${acquiringEntity.name}, mongo id: ${acquiringEntity._id} and register number: ${acquiringEntity.registrationNumber} has been checked in Vmss`
    );
  } catch (error: any) {
    const originalError = safeJsonParse(error.message);

    if (acquiringEntity?.visaMatchData) {
      acquiringEntity.visaMatchData.push({
        errorMessageRequest: originalError
      });
    } else {
      acquiringEntity.visaMatchData = [
        {
          errorMessageRequest: originalError
        }
      ];
    }

    acquiringEntity.save().catch((error: any) => {
      visaVmssLogger.error(
        `Failed to save acquiring entity match with mongo id: ${acquiringEntity._id} in mongodb, saved error: ${error.message}`
      );
    });

    throw new CustomError(
      `Failed to check acquiring entity match with mongo id: ${acquiringEntity._id} in Visa Vmss: ${error.message}`,
      400
    );
  }
}

// This function is used to check initial acquiring entity match in mastercard
export async function checkAcquiringEntityMatchInMastercard(acquiringEntityId: string) {
  // We start with page_offset = 0 because mastercard api is 0 based
  const page_length = 1;
  const page_offset = 0;
  let acquiringEntity: AcquiringEntityModelDocument | null = null;

  acquiringEntity = await getAcquiringEntityById(acquiringEntityId);
  if (!acquiringEntity) {
    throw new CustomError('Acquiring entity not found', 404);
  }

  let mastercardMatchProDataArrayResponse: {
    terminationInquiryResponse?: TerminationInquiryDTO;
    error?: any;
    url: string | undefined;
  }[] = [];

  try {
    mastercardMatchProDataArrayResponse = await acquiringEntityMatchPro(acquiringEntity, page_length, page_offset);
    const mastercardMatchDataItems: MastercardMatchDataItem[] = [];
    let successRequest = 0;
    for (const mastercardMatchProData of mastercardMatchProDataArrayResponse) {
      try {
        if (mastercardMatchProData.error) {
          throw new Error(mastercardMatchProData.error);
        }
        const totalCountMatches =
          mastercardMatchProData?.terminationInquiryResponse?.possibleMerchantMatches?.[0]?.totalMerchantMatches;
        if (totalCountMatches == null || isNaN(totalCountMatches)) {
          throw new Error('Invalid totalCountMatches: Value is null, undefined, or not a number.');
        }
        const mastercardMatch = totalCountMatches > 0;
        const refNumber = mastercardMatchProData?.terminationInquiryResponse?.ref;
        const data: MastercardMatchDataItem = {
          url: mastercardMatchProData.url,
          isMatch: mastercardMatch,
          mastercardTotalCountMatches: totalCountMatches,
          mastercardRefNumberMatched: refNumber
        };
        mastercardMatchDataItems.push(data);
        successRequest++;
      } catch (error: any) {
        mastercardMatchDataItems.push({
          errorMessage: error.message
        });
      }
    }

    let isThereAnyMatch = false;
    mastercardMatchDataItems.forEach((item: MastercardMatchDataItem) => {
      if (item.isMatch) {
        isThereAnyMatch = true;
      }
    });

    const matchData: MastercardMatchData = {
      requestMadeToCheckMerchant: mastercardMatchProDataArrayResponse.length,
      data: mastercardMatchDataItems,
      failedRequest: mastercardMatchProDataArrayResponse.length - successRequest,
      isThereAnyMatch: isThereAnyMatch
    };

    if (acquiringEntity.mastercardMatchData) {
      acquiringEntity.mastercardMatchData.push(matchData);
    } else {
      acquiringEntity.mastercardMatchData = [matchData];
    }

    await acquiringEntity.save();
    // .catch((error: any) => {
    //   mastercardLogger.error(
    //     `Failed to save acquiring entity with id: ${acquiringEntity._id} match in Mastercard, saved error: ${error.message}`
    //   );
    // });
    mastercardLogger.info(
      `Acquiring entity with name: ${acquiringEntity.name}, mongo id: ${acquiringEntity._id} and register number: ${acquiringEntity.registrationNumber} has been checked in Mastercard`
    );
  } catch (error: any) {
    const originalError = safeJsonParse(error.message);

    if (acquiringEntity?.mastercardMatchData) {
      acquiringEntity.mastercardMatchData.push({
        errorMessageRequest: originalError
      });
    } else {
      acquiringEntity.mastercardMatchData = [
        {
          errorMessageRequest: originalError
        }
      ];
    }

    await acquiringEntity.save().catch((error: any) => {
      mastercardLogger.error(
        `Failed to save acquiring entity match with mongo id: ${acquiringEntity._id} in mongodb, saved error: ${error.message}`
      );
    });
    throw new CustomError(
      `Failed to check acquiring entity match with mongo id: ${acquiringEntity._id} in Mastercard: ${error.message}`,
      400
    );
  }
}

export async function manualApproveAcquireMastercard(data: ManualApproveAcquire) {
  const { acquiringEntityId, typeOfAction, matchDataId } = data;

  const acquiringEntity: AcquiringEntityModelDocument | null = await getAcquiringEntityById(acquiringEntityId);

  if (!acquiringEntity) {
    throw new CustomError('Acquiring entity not found', 404);
  }

  for (let i = (acquiringEntity.mastercardMatchData?.length as number) - 1; i >= 0; i--) {
    const match: MastercardMatchData | undefined = acquiringEntity.mastercardMatchData?.[i];

    if (match?._id?.toString() === matchDataId) {
      match.resolvedMastercardMatch = typeOfAction === 'approve' ? true : false;
      break;
    }
  }

  await acquiringEntity.save();

  // We do not need to wait for the sumsub applicant to be created
  acquiringEntitySumsubApplicantFlow(acquiringEntityId);

  return acquiringEntity;
}

export async function manualApproveAcquireMastercardByRefNumber(data: ManualApproveAcquireByRefNumber) {
  const { acquiringEntityId, typeOfAction, mastercardRefNumberMatched } = data;

  const acquiringEntity: AcquiringEntityModelDocument | null = await getAcquiringEntityById(acquiringEntityId);

  if (!acquiringEntity) {
    throw new CustomError('Acquiring entity not found', 404);
  }

  if (!acquiringEntity.mastercardMatchData || acquiringEntity.mastercardMatchData.length <= 0) {
    throw new CustomError(
      `Acquiring entity with this id: ${acquiringEntityId} does not pass Mastercard screening.`,
      404
    );
  }

  let matchReferenceData: MastercardMatchDataItem | undefined = undefined;
  for (let i = (acquiringEntity.mastercardMatchData?.length as number) - 1; i >= 0; i--) {
    const match: MastercardMatchData | undefined = acquiringEntity.mastercardMatchData?.[i];
    const matchItem: MastercardMatchDataItem | undefined = match?.data?.find((item: MastercardMatchDataItem) => {
      return item.mastercardRefNumberMatched === mastercardRefNumberMatched;
    });
    if (matchItem) {
      if (typeOfAction === 'approve') {
        matchItem.resolvedMastercardMatch = true;
      } else {
        matchItem.resolvedMastercardMatch = false;
      }
      matchReferenceData = matchItem;
      break;
    }
  }

  if (!matchReferenceData) {
    throw new CustomError(
      `Acquiring entity with this id: ${acquiringEntityId} does not have any match reference data with this ref number: ${mastercardRefNumberMatched}.`,
      404
    );
  }

  await acquiringEntity.save();

  return acquiringEntity;
}

export async function manualApproveAcquireVisaVmss(data: ManualApproveAcquire) {
  const { acquiringEntityId, typeOfAction, matchDataId } = data;

  const acquiringEntity: AcquiringEntityModelDocument | null = await getAcquiringEntityById(acquiringEntityId);

  if (!acquiringEntity) {
    throw new CustomError('Acquiring entity not found', 404);
  }

  for (let i = (acquiringEntity.visaMatchData?.length as number) - 1; i >= 0; i--) {
    const match: VisaMatchData | undefined = acquiringEntity.visaMatchData?.[i];
    if (match?._id?.toString() === matchDataId) {
      match.resolvedVisaMatch = typeOfAction === 'approve' ? true : false;
      break;
    }
  }

  await acquiringEntity.save();

  // We do not need to wait for the sumsub applicant to be created
  acquiringEntitySumsubApplicantFlow(acquiringEntityId);

  return acquiringEntity;
}

export async function getAcquiringEntityWithAllProperties(acquiringEntityId: string) {
  const acquiringEntityDoc = await getAcquiringEntityById(acquiringEntityId);

  if (!acquiringEntityDoc) {
    throw new CustomError('Acquiring entity not found!', 404);
  }

  // Convert the Mongoose document to a plain JavaScript object
  const acquiringEntity = acquiringEntityDoc.toObject();

  // Define default values for missing properties
  const defaultAcquiringEntity: Partial<AcquiringEntity> = {
    email: '',
    phone: [],
    country: '',
    state: '',
    city: '',
    zip: '',
    address1: '',
    address2: '',
    tradeOverInternet: false,
    websites: [],
    principalsIds: [],
    principalRawData: [],
    businessCategoryVmss: BusinessCategoryVmss.MERCHANT,
    taxId: ''
  };

  const defaultPrincipal: Partial<Principal> = getPrincipalDefaultValues();

  const acquiringEntityWithAllProperties = { ...defaultAcquiringEntity, ...acquiringEntity };
  delete acquiringEntityWithAllProperties.principalRawData;
  delete acquiringEntityWithAllProperties.isAllWebsitesClassified;
  delete acquiringEntityWithAllProperties.__v;
  delete acquiringEntityWithAllProperties.createdAt;
  delete acquiringEntityWithAllProperties.updatedAt;
  delete acquiringEntityWithAllProperties.taxId;
  delete acquiringEntityWithAllProperties.visaMatchData;
  delete acquiringEntityWithAllProperties.mastercardMatchData;
  delete acquiringEntityWithAllProperties.history;
  delete acquiringEntityWithAllProperties?.sumsub?.createdAt;
  delete acquiringEntityWithAllProperties?.sumsub?.updatedAt;

  if (acquiringEntityWithAllProperties.principalsIds?.length > 0) {
    acquiringEntityWithAllProperties.principalsIds = acquiringEntityWithAllProperties.principalsIds.map(
      (principalId: PrincipalModelDocument) => {
        const principal = principalId.toObject ? principalId.toObject() : principalId; // Ensure conversion

        const principalWithAllProperties = { ...defaultPrincipal, ...principal };
        delete principalWithAllProperties.__v;
        delete principalWithAllProperties.createdAt;
        delete principalWithAllProperties.updatedAt;

        return principalWithAllProperties;
      }
    );
  }

  if (acquiringEntityWithAllProperties.websites?.length > 0) {
    acquiringEntityWithAllProperties.websites = acquiringEntityWithAllProperties.websites.map((website: any) => {
      delete website?.mccClassification?.createdAt;
      delete website?.mccClassification?.updatedAt;
      delete website?.mccClassification?.retryCount;
      delete website?.sumsub?.createdAt;
      delete website?.sumsub?.updatedAt;

      return website;
    });
  }
  return acquiringEntityWithAllProperties;
}

export async function acquiringEntityUpdate(data: AcquiringEntity & { _id: string }, username: string) {
  // Fields that shouldn't be updated
  const blockedFields = [
    'isAllWebsitesClassified',
    'sumsub',
    'principalRawData',
    'submissionId',
    'visaMatchData',
    'mastercardMatchData',
    'createdAt',
    'updatedAt'
  ];

  let executeNewMatchCheck = false;
  let isAllWebsitesClassified = false;

  // We are using this function to get the acquiring entity with all properties because this
  // function return the fields witch can be changed in to the dashboard
  const acquiringEntity = await getAcquiringEntityWithAllProperties(data._id);
  const acquiringEntityDocument = await AcquiringEntityModel.findById(data._id);
  if (!acquiringEntityDocument) {
    throw new CustomError('Acquiring entity not found!', 404);
  }

  if (!acquiringEntity) {
    throw new CustomError('Acquiring entity not found!', 404);
  }

  if (!data.principalsIds) {
    throw new Error('No principals found');
  }
  const originalAcquiringEntity = { ...acquiringEntity };

  // Step 2: Update Company Information
  const dataUpdate = { ...data };
  // @ts-ignore
  delete dataUpdate?._id;
  delete dataUpdate.principalsIds;

  // Remove blocked fields
  for (const field of blockedFields) {
    delete dataUpdate[field as keyof typeof dataUpdate];
  }

  const updateData = dataUpdate as Record<string, any>;

  const changes: DataChanged[] = [];

  // Only compare fields that exist in the update data
  Object.keys(updateData).forEach((key) => {
    // Skip comparison for certain fields that should not be tracked
    if (['websites', 'principalsIds', 'sumsub'].includes(key)) {
      return;
    }

    const oldValue = acquiringEntity[key];
    const newValue = updateData[key];
    // Handle special case for boolean fields that might come as strings
    if (typeof oldValue === 'boolean' && typeof newValue === 'string') {
      const normalizedNewValue = newValue.toLowerCase() === 'true';
      if (oldValue !== normalizedNewValue) {
        changes.push({
          field: key,
          oldValue,
          newValue: normalizedNewValue
        });
      }
      return;
    }

    if (key == 'phone' && Array.isArray(oldValue) && Array.isArray(newValue)) {
      const differences = JSON.stringify(oldValue) !== JSON.stringify(newValue);
      if (differences) {
        changes.push({
          field: key,
          oldValue,
          newValue
        });
      }

      return;
    }

    // Only track changes if values are different
    if (oldValue !== newValue) {
      changes.push({
        field: key,
        oldValue,
        newValue
      });
    }
  });
  let skipMccClassification = true;
  // Remove nested field websites.sumsub.sumsubId
  if (Array.isArray(dataUpdate.websites)) {
    let countClassifiedWebsites = 0;
    dataUpdate.websites = dataUpdate.websites.map((website: any, index: number) => {
      const originalWebsite = originalAcquiringEntity.websites?.[index];
      const incomingMcc = website?.mccClassification?.mcc;
      const originalMcc = originalWebsite?.mccClassification?.mcc;
      if (website.url !== originalWebsite?.url) {
        skipMccClassification = false;
        website.isWebsiteClassified = false;
        website.mccClassification.retryCount = 0;
        dataUpdate.isAllWebsitesClassified = false;
      }
      // If the MCC has changed, we need to execute a new match check
      if (incomingMcc && incomingMcc !== originalMcc) {
        executeNewMatchCheck = true;
      }

      if (website?.mccClassification?.mcc) {
        countClassifiedWebsites++;
      }
      const websiteDifferences = getObjectDifferences(originalWebsite, website);

      if (websiteDifferences.length > 0) {
        changes.push(...websiteDifferences);
      }

      if (website?.sumsub) {
        delete website.sumsub;
      }
      return website;
    });

    if (countClassifiedWebsites === dataUpdate.websites.length) {
      isAllWebsitesClassified = true;
    }
  }

  dataUpdate.isAllWebsitesClassified = isAllWebsitesClassified;

  const updatedAcquiringEntity = await AcquiringEntityModel.findByIdAndUpdate(data._id, dataUpdate, { new: true });
  if (!updatedAcquiringEntity) {
    throw new Error('Failed to update acquiring entity');
  }

  // Step 3: Update Each Principal One by One
  const updatedPrincipals = [];
  const originalPrincipals: any[] = [];
  const deletedPrincipals: any[] = [];

  try {
    for (const [index, principalUpdate] of (data.principalsIds as unknown as PrincipalModelDocument[]).entries()) {
      const { _id, ...updateData } = principalUpdate;
      if (_id) {
        const principal = await getPrincipalById(_id as string);
        if (!principal) {
          throw new Error(`Principal with ID ${_id} not found`);
        }

        const originalPrincipal = { ...principal.toObject() };
        originalPrincipals.push(originalPrincipal);

        // If we do not have firstName than we delete the principal
        if (updateData.firstName == null || updateData.firstName.length <= 0) {
          await deletePrincipal(principal._id as string);
          changes.push({
            field: `principalsIds[${index}]`,
            oldValue: principal,
            newValue: null
          });
          deletedPrincipals.push(originalPrincipal);

          acquiringEntityDocument.principalsIds = acquiringEntityDocument.principalsIds?.filter(
            (principalId: any) => principalId._id.toString() !== _id.toString()
          );

          await acquiringEntityDocument.save();
        } else {
          const updatedPrincipal = await updatePrincipal(_id as string, updateData);
          if (!updatedPrincipal) {
            throw new Error(`Failed to update principal with ID ${_id}`);
          }

          const differences = getDifferencesInArraysWhenInsideArrayIsObject(
            [originalPrincipal],
            [updatedPrincipal.toObject()]
          );

          if (differences.length > 0) {
            changes.push(...differences);
          }

          updatedPrincipals.push(updatedPrincipal);
        }
      } else {
        if (!principalUpdate.firstName) {
          continue;
        }

        const principal = await createPrincipal(updateData as Principal);
        changes.push({
          field: `principalsIds[${index}]`,
          oldValue: null,
          newValue: principal
        });
        if (principal) {
          acquiringEntityDocument.principalRawData = [...(acquiringEntity.principalRawData || []), updateData];
          // @ts-ignore
          acquiringEntityDocument.principalsIds = [...(acquiringEntity.principalsIds || []), principal._id as ObjectId];

          await acquiringEntityDocument.save();
          updatedPrincipals.push(principal);
        } else {
          throw new Error('Failed to create principal');
        }
      }
    }

    if (changes.length > 0) {
      executeNewMatchCheck = true;

      if (acquiringEntityDocument.history && acquiringEntityDocument?.history?.length > 0) {
        acquiringEntityDocument.history.push({
          dataChanged: changes,
          changedAt: new Date(),
          changedBy: username
        });
      } else {
        acquiringEntityDocument.history = [
          {
            dataChanged: changes,
            changedAt: new Date(),
            changedBy: username
          }
        ];
      }
    }
    await acquiringEntityDocument.save();

    if (executeNewMatchCheck) {
      processAcquiringEntityScreening(acquiringEntity._id as string, skipMccClassification);
    }

    if (!skipMccClassification) {
      return 'You will need to wait around 2 minutes to classify all websites!';
    }

    return 'Successfully updated';
  } catch (error: any) {
    acquiringEntityLogger.error(`Failed to update acquiring entity: ${error.message}`);

    // Rollback company and previously updated principals
    await AcquiringEntityModel.findByIdAndUpdate(data._id, originalAcquiringEntity, { new: true });
    await Promise.all(
      updatedPrincipals.map(async (principal) => {
        const originalPrincipal = originalPrincipals.find(
          (p) => p._id.toString() === (principal?._id as string).toString()
        );

        if (originalPrincipal) {
          await updatePrincipal(principal._id as string, originalPrincipal);
        } else {
          await deletePrincipal(principal._id as string);
        }
        return null;
      })
    );
    // Rollback: Restore deleted principals
    await Promise.all(
      deletedPrincipals.map(async (principal) => {
        await createPrincipal(principal);
      })
    );
    throw new Error(error.message);
  }
}

function getObjectDifferences(obj1: any, obj2: any, path: string = ''): any[] {
  const differences: any[] = [];

  const keys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);

  keys.forEach((key) => {
    const fullPath = path ? `${path}.${key}` : key;
    const val1 = obj1[key];
    const val2 = obj2[key];

    if (typeof val1 === 'object' && val1 !== null && typeof val2 === 'object' && val2 !== null) {
      differences.push(...getObjectDifferences(val1, val2, fullPath));
    } else if (val1 != val2) {
      // loose comparison
      differences.push({
        field: fullPath,
        oldValue: val1,
        newValue: val2
      });
    }
  });

  return differences;
}

function getDifferencesInArraysWhenInsideArrayIsObject(array1: any[], array2: any[]): any[] {
  const allDiffs: any[] = [];

  if (array1.length !== array2.length) {
    allDiffs.push({
      key: 'array.length',
      oldValue: array1.length,
      newValue: array2.length
    });
  }

  array1.forEach((obj, index) => {
    const diffs = getObjectDifferences(obj, array2[index] || {}, `index[${index}]`);
    allDiffs.push(...diffs);
  });

  return allDiffs;
}

export async function getAcquiringEntityMatchInVmss(acquiringEntityId: string) {
  let acquiringEntity: AcquiringEntityModelDocument | null = null;
  try {
    acquiringEntity = await getAcquiringEntityById(acquiringEntityId);

    if (!acquiringEntity) {
      throw new CustomError('Acquiring entity with id: ${acquiringEntityId} not found', 404);
    }

    return acquiringEntity;
  } catch (error: any) {
    if (acquiringEntity) {
      // acquiringEntity.visaErrorMatch = error.message;
      await acquiringEntity.save().catch((err: any) => {
        acquiringEntityLogger.error(`Failed to save acquiring entity with Vmss error, saved error: ${err.message}`);
      });
    }
    acquiringEntityLogger.error(`Failed to check acquiring entity match in Vmss: ${error.message}`);
    throw error;
  }
}

export async function createAcquiringEntityInSumsubApplicant(acquiringEntityId: string): Promise<boolean> {
  let acquiringEntity: AcquiringEntityModelDocument | null = null;
  try {
    acquiringEntity = await getAcquiringEntityById(acquiringEntityId);

    if (!acquiringEntity) {
      throw new Error(`Acquiring entity with this id: ${acquiringEntityId} not found`);
    }

    const country = getThreeLetterCodeFromFullName(acquiringEntity.country as string);

    if (!country) {
      throw new Error(`Country with this name: ${acquiringEntity.country} not found in his three letter code.`);
    }

    const levelName = SUMSUB.ACQUIRING_ENTITY_LEVEL_NAME;
    const sumsubData = {
      // externalUserId: '20',
      type: 'company' as 'company' | 'individual',
      externalUserId: acquiringEntity._id as string,
      fixedInfo: {
        companyInfo: {
          companyName: acquiringEntity.name,
          registrationNumber: acquiringEntity.registrationNumber,
          country: country
        }
      }
    };

    const sumsubApplicant = await createApplicant(levelName, sumsubData);
    let sumsubId: string | null = null;
    if (sumsubApplicant?.data?.id) {
      sumsubId = sumsubApplicant.data.id as string;
      if (acquiringEntity.sumsub) {
        acquiringEntity.sumsub.sumsubId = sumsubId;
        acquiringEntity.sumsub.createApplicantError = undefined;
      } else {
        acquiringEntity.sumsub = {
          sumsubId: sumsubId,
          createApplicantError: undefined
        };
      }
      await acquiringEntity.save();
    }

    if (sumsubId) {
      try {
        await rumAMLApplicantCheck(sumsubId);
      } catch (error: any) {
        if (acquiringEntity.sumsub) {
          acquiringEntity.sumsub.AMLError = safeJsonParse(error.message);
        } else {
          acquiringEntity.sumsub = {
            AMLError: safeJsonParse(error.message)
          };
        }
        await acquiringEntity.save().catch((err: any) => {
          acquiringEntityLogger.error(
            `Failed to save acquiring entity with Sumsub AMLerror:${error.message}, saved error: ${err.message}`
          );
        });
      }
    }
    return true;
  } catch (error: any) {
    if (acquiringEntity) {
      const message = safeJsonParse(error.message);
      if (acquiringEntity.sumsub) {
        acquiringEntity.sumsub.createApplicantError = message;
      } else {
        acquiringEntity.sumsub = {
          createApplicantError: message
        };
      }

      acquiringEntity.save().catch((err: any) => {
        acquiringEntityLogger.error(
          `Failed to save acquiring entity with Sumsub error:${error.message}, saved error: ${err.message}`
        );
      });
    }
    acquiringEntityLogger.error(`Error creating acquiring entity in Sumsub: ${error.message}`);
    return false;
  }
}

export async function handleSumsubApplicantAcquiringEntityDataWebhook(_topic: string, data: WebhookApplicant) {
  const userMongoId = data?.externalUserId;

  let acquiringEntity: AcquiringEntityModelDocument | null = null;
  try {
    if (!userMongoId) {
      throw new HttpException(400, `Webhook data does not provide externalUserId: ${userMongoId}.`);
    }
    try {
      // Find user by externalUserId and check AML
      acquiringEntity = await getAcquiringEntityById(userMongoId);
    } catch (error) {
      throw new HttpException(
        400,
        `Something went wrong when we try to find user with externalUserId: ${userMongoId}.`
      );
    }

    if (!acquiringEntity) {
      throw new HttpException(400, `We do not have record with this acquiring entity mongo id: ${userMongoId}.`);
    }

    if (
      data?.reviewResult?.reviewAnswer == ReviewAnswer.Red ||
      data?.reviewResult?.reviewAnswer == ReviewAnswer.Green
    ) {
      if (acquiringEntity.sumsub) {
        acquiringEntity.sumsub.reviewStatus = data.reviewStatus as ReviewStatus;
        acquiringEntity.sumsub.reviewAnswer = data.reviewResult.reviewAnswer as ReviewAnswer;
        acquiringEntity.sumsub.AMLError = data.reviewResult?.moderationComment
          ? data.reviewResult.moderationComment
          : undefined;
      } else {
        acquiringEntity.sumsub = {
          reviewStatus: data.reviewStatus as ReviewStatus,
          reviewAnswer: data.reviewResult.reviewAnswer as ReviewAnswer,
          AMLError: data.reviewResult?.moderationComment ? data.reviewResult.moderationComment : undefined
        };
      }

      try {
        await acquiringEntity.save();
      } catch (error: any) {
        const message = `Failed to saved a acquiring entity with mongoId: ${data.externalUserId} error: ${error.message}.`;
        acquiringEntityLogger.error(message);
        throw new Error(message);
      }
    } else {
      throw new HttpException(400, `Unknown reviewAnswer: ${data.reviewResult.reviewAnswer}.`);
    }
  } catch (error: any) {
    acquiringEntityLogger.error(`${error.message}`);
  }
}

export async function acquireEntityClassificationUrls(
  acquiringEntityId: string
): Promise<AcquiringEntityModelDocument> {
  const acquiringEntity = await getAcquiringEntityById(acquiringEntityId);
  if (!acquiringEntity) {
    throw new Error(`Acquiring entity with this id: ${acquiringEntityId} not found`);
  }

  let countClassifiedWebsites = 0;

  const websites = acquiringEntity.websites;
  if (!websites || websites.length <= 0) {
    return acquiringEntity;
  }
  for (const website of websites) {
    if (website.isWebsiteClassified) {
      countClassifiedWebsites++;
      continue;
    }
    if (!website.url) {
      continue;
    }

    try {
      if (website.mccClassification?.retryCount && website.mccClassification.retryCount >= 3) {
        throw new Error(`Failed to classify website with url: ${website.url} reached max retries 3`);
      }
      const mccClassification: MCCClassification = await getMccClassification(website.url);

      website.mccClassification = {
        url: website.url,
        mcc: mccClassification.mcc,
        certainty: mccClassification.certainty,
        description: mccClassification.description,
        errorMessage: undefined,
        retryCount: website.mccClassification?.retryCount ? website.mccClassification.retryCount + 1 : 1
      };
      website.isWebsiteClassified = true;
      countClassifiedWebsites++;
      mccClassificationLogger.info(
        `Successfully classified website with url: ${website.url} for acquiring entity with id: ${acquiringEntityId}`
      );
    } catch (error: any) {
      website.mccClassification = {
        url: website.url,
        errorMessage: error.message,
        retryCount: website.mccClassification?.retryCount ? website.mccClassification.retryCount + 1 : 1
      };
      website.isWebsiteClassified = false;
      mccClassificationLogger.info(
        `Failed to classify website with url: ${website.url} for acquiring entity with id: ${acquiringEntityId}`
      );
    }

    // Save after each update to persist progress
    try {
      await acquiringEntity.save();
    } catch (err: any) {
      acquiringEntityLogger.error(
        `Failed to save acquiring entity with MCC classification for URL: ${website.url} error: ${err.message}`
      );
    }
  }

  const isAllWebsitesClassified = countClassifiedWebsites === websites.length;

  acquiringEntity.isAllWebsitesClassified = isAllWebsitesClassified;
  await acquiringEntity.save().catch((err: any) => {
    acquiringEntityLogger.error(
      `Failed to save acquiring entity with id: ${acquiringEntity._id}, isAllWebsitesClassified: ${isAllWebsitesClassified} error: ${err.message}`
    );
  });

  return acquiringEntity;
}

export async function processAcquiringEntityScreening(
  acquiringEntityId: string,
  skipMccClassification: boolean = false
) {
  try {
    if (!skipMccClassification) {
      await acquireEntityClassificationUrls(acquiringEntityId);
    }
  } catch (error: any) {
    mccClassificationLogger.error(
      `Error processing AI MCC classification for acquiring entity with id: ${acquiringEntityId} error: ${error.message}`
    );
    return false;
  }
  try {
    await checkAcquiringEntityMatchInVmss(acquiringEntityId);
  } catch (error: any) {
    visaVmssLogger.error(` VISA VMSS merchant error: ${error.message}`);
  }

  try {
    await checkAcquiringEntityMatchInMastercard(acquiringEntityId);
  } catch (error: any) {
    mastercardLogger.error(`Mastercard merchant error: ${error.message}`);
  }

  try {
    await sendForOnboarding(acquiringEntityId);
  } catch (error: any) {
    onboardingLogger.error(`sendForOnboarding-> For acquiringEntityId: ${acquiringEntityId} error: ${error.message}`);
  }
  await acquiringEntitySumsubApplicantFlow(acquiringEntityId);
}

export async function acquiringEntitySumsubApplicantFlow(acquiringEntityId: string) {
  try {
    const acquiringEntity = await getAcquiringEntityById(acquiringEntityId);

    if (!acquiringEntity) {
      throw new Error(`Acquiring entity with this id: ${acquiringEntityId} not found`);
    }
    if (!acquiringEntity.visaMatchData || acquiringEntity.visaMatchData?.length <= 0) {
      throw new Error(`Acquiring entity with this id: ${acquiringEntityId} does not pass Visa VMSS screening.`);
    }

    if (!acquiringEntity.mastercardMatchData || acquiringEntity.mastercardMatchData?.length <= 0) {
      throw new Error(`Acquiring entity with this id: ${acquiringEntityId} does not pass Mastercard screening.`);
    }

    const lastVisaMatchData: VisaMatchData = acquiringEntity.visaMatchData[acquiringEntity.visaMatchData.length - 1];
    const lastMastercardMatchData: MastercardMatchData =
      acquiringEntity.mastercardMatchData[acquiringEntity.mastercardMatchData.length - 1];
    if (
      lastVisaMatchData?.isThereAnyMatch ||
      lastVisaMatchData?.errorMessageRequest ||
      (lastVisaMatchData?.failedRequest && lastVisaMatchData?.failedRequest > 0)
    ) {
      throw new Error(
        `Acquiring entity with this id: ${acquiringEntityId} does not pass Visa VMSS screening.(due to isThereAnyMatch or errorMessageRequest or failedRequest)`
      );
    }

    if (
      lastMastercardMatchData?.isThereAnyMatch ||
      lastMastercardMatchData?.errorMessageRequest ||
      (lastMastercardMatchData?.failedRequest && lastMastercardMatchData?.failedRequest > 0)
    ) {
      throw new Error(
        `Acquiring entity with this id: ${acquiringEntityId} does not pass Mastercard screening.(due to isThereAnyMatch or errorMessageRequest or failedRequest)`
      );
    }

    // Check if applicant already exists in Sumsub
    if (!acquiringEntity.sumsub?.sumsubId) {
      await createAcquiringEntityInSumsubApplicant(acquiringEntityId);

      // :TODO need to put in the correct place in the flow
      await createSumsubApplicantsForAcquiringEntityBeneficiaries(acquiringEntityId);
    }
  } catch (error: any) {
    acquiringEntityLogger.error(
      `Error processing acquiring entity with id: ${acquiringEntityId}, for Sumsub create applicant with error: ${error.message}`
    );
  }
}

export async function getAllMatchReferenceForMastercardDashboard(
  acquiringEntityId: string,
  page_length: number,
  page_offset: number
) {
  const acquiringEntity = await getAcquiringEntityById(acquiringEntityId);
  if (!acquiringEntity) {
    throw new Error(`Acquiring entity with this id: ${acquiringEntityId} not found`);
  }

  const lastMastercardMatchData: MastercardMatchData | null = acquiringEntity.mastercardMatchData
    ? acquiringEntity.mastercardMatchData[acquiringEntity.mastercardMatchData.length - 1]
    : null;

  if (!lastMastercardMatchData) {
    throw new Error(`Acquiring entity with this id: ${acquiringEntityId} does not have any mastercard match data`);
  }
  const paginationData = getPaginationData(lastMastercardMatchData.data?.length || 0, page_length, page_offset + 1);

  return {
    ...paginationData,
    _id: lastMastercardMatchData._id,
    // @ts-ignore
    createdAt: lastMastercardMatchData.createdAt,
    failedRequests: lastMastercardMatchData.failedRequest,
    totalRequests: lastMastercardMatchData.requestMadeToCheckMerchant,
    docs: lastMastercardMatchData?.data
  };
}

export async function createSumsubApplicantsForAcquiringEntityBeneficiaries(acquiringEntityId: string) {
  const acquiringEntity = await getAcquiringEntityById(acquiringEntityId);
  if (!acquiringEntity) {
    throw new Error(`Acquiring entity with this id: ${acquiringEntityId} not found`);
  }

  const beneficiaries = acquiringEntity.principalsIds;
  if (!beneficiaries || beneficiaries.length <= 0) {
    return;
  }

  for (const beneficiary of beneficiaries) {
    const beneficiaryDoc = beneficiary as unknown as PrincipalModelDocument;
    const isCreated = await createSumsubApplicantForPrincipal((beneficiaryDoc._id as ObjectId).toString());

    if (!isCreated) {
      continue;
    }

    // We do not need to wait for this function to finish
    // we can run it asynchronously
    connectBeneficiaryToCompany(
      (acquiringEntity._id as ObjectId).toString(),
      acquiringEntity.sumsub?.sumsubId as string,
      (beneficiaryDoc._id as ObjectId).toString()
    );

    let link: string | undefined;
    try {
      link = await getSumsubApplicantWebSDKLinkForPrincipal((beneficiaryDoc._id as ObjectId).toString());

      if (!link) {
        throw new Error(`Sumsub applicant web SDK link for principal with id: ${beneficiaryDoc._id} is not provided`);
      }
    } catch (error: any) {
      const message = `Error generating Sumsub applicant web SDK link for principal with id: ${beneficiaryDoc._id} error: ${error.message}`;
      acquiringEntityLogger.error(message);
      if (beneficiaryDoc?.sumsub?.additionalErrors) {
        beneficiaryDoc.sumsub.additionalErrors = message;
      } else {
        if (!beneficiaryDoc.sumsub) {
          beneficiaryDoc.sumsub = {};
        }
        beneficiaryDoc.sumsub.additionalErrors = message;
      }
      await beneficiaryDoc.save();
      continue;
    }

    try {
      const email = beneficiaryDoc.email;
      if (!email) {
        throw new Error(`Principal with id: ${beneficiaryDoc._id} does not have an email`);
      }
      const data: AcquiringEntityBeneficiaryWebSDKLinkEmail = {
        firstName: beneficiaryDoc.firstName,
        lastName: beneficiaryDoc.lastName,
        link,
        email
      };
      await publishAcquiringEntityBeneficiaryWebSDKLink((beneficiaryDoc._id as ObjectId).toString(), data);
    } catch (error: any) {
      const message = `Error on sending email to acquiring entity beneficiary for web SDK link to principal with id: ${beneficiaryDoc._id} error: ${error.message}`;
      acquiringEntityLogger.error(message);
      if (beneficiaryDoc?.sumsub?.additionalErrors) {
        beneficiaryDoc.sumsub.additionalErrors = message;
      } else {
        if (!beneficiaryDoc.sumsub) {
          beneficiaryDoc.sumsub = {};
        }
        beneficiaryDoc.sumsub.additionalErrors = message;
      }
      await beneficiaryDoc.save();
    }
  }
}

export async function connectBeneficiaryToCompany(companyId: string, companyApplicantId: string, principalId: string) {
  let principal: PrincipalModelDocument | null = null;
  try {
    principal = await getPrincipalById(principalId);
    if (!principal) {
      throw new Error(`Principal with this id: ${principalId} not found`);
    }
    const data: LinkBeneficiaryToCompany = {
      companyApplicantId: companyApplicantId,
      beneficiaryData: {
        applicantId: principal.sumsub?.sumsubId,
        // @ts-ignore
        types: [principal.positionInCompany?.toLowerCase()]
      }
    };
    const linkBeneficiaryResponse: LinkBeneficiaryResponse = await linkBeneficiaryToCompany(data);

    acquiringEntityLogger.info(`Beneficiary with id:${principal._id} is linked to company with id:${companyId}.`);
  } catch (error: any) {
    if (principal) {
      principal.connectBeneficiaryToCompanyError = error.message;
      await principal.save().catch((err) => {
        acquiringEntityLogger.error(`Error on saving principal with id: ${principalId} error: ${err.message}`);
      });
    }
    acquiringEntityLogger.error(
      `Error on connecting principal: ${principalId} to company for acquiring entity with id: ${companyApplicantId} error: ${error.message}`
    );
  }
}
