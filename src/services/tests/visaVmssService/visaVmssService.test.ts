// Mock environment variables before imports
process.env.SUMSUB_RYVYL_SERVICE_BASE_URL = 'http://mock-sumsub-url';
process.env.RYVYL_MASTERCARD_MATCH_PRO_SERVICE_BASE_URL = 'http://mock-mastercard-url';
process.env.RYVYL_VISA_VMSS_SERVICE_BASE_URL = 'http://mock-visa-url';
process.env.SUMSUB_ACQUIRING_ENTITY_URL_LEVEL_NAME = 'mock-level';
process.env.SUMSUB_ACQUIRING_ENTITY_BENEFICIARY_LEVEL_NAME = 'mock-beneficiary-level';
process.env.MCC_SERVICE_CLASSIFIER_BASE_URL = 'http://mock-mcc-classifier-url';

import { CustomError } from '../../../classes/CustomError';
import { AcquiringEntityModelDocument } from '../../../models/mongo/acquiringEntity';
import {
  vmssAcquiringEntityMatchWithDescriptionInformation,
  getAcquiringEntityMatchInVisaVmssDashboard
} from '../../visaVmssService';
import { getAcquiringEntityById } from '../../acquiringEntityService';
import { checkAcquiringEntityRequest } from '../../../requests/ryvylVisaVmssRequests';
import { VisaMatchData, VisaMatchDataItem } from '../../../interfaces/visaVmssInterface';
import * as countryFormat from '../../../utils/countryFormat';
import { BusinessCategoryVmss } from '../../../enums/businessCategory';
import { Types } from 'mongoose';

// Mock dependencies
jest.mock('../../../models/mongo/acquiringEntity');
jest.mock('../../acquiringEntityService');
jest.mock('../../../requests/ryvylVisaVmssRequests');
jest.mock('../../../utils/countryFormat');
jest.mock('../../../utils/logger', () => ({
  visaVmssLogger: {
    error: jest.fn(),
    info: jest.fn()
  }
}));

describe('Visa VMSS Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('vmssAcquiringEntityMatchWithDescriptionInformation', () => {
    it('should process acquiring entity match successfully with websites', async () => {
      // Arrange
      const mockWebsites = [
        { url: 'https://example.com', mccClassification: { mcc: '1234' } },
        { url: 'https://test.com', mccClassification: { mcc: '5678' } }
      ];

      // Mock principal data
      const mockPrincipals = [
        {
          _id: 'principal-1',
          firstName: 'John',
          lastName: 'Doe',
          phone: '1234567890',
          address1: '123 Main St',
          city: 'Test City',
          country: 'United States',
          state: 'CA',
          zip: '12345',
          email: '<EMAIL>'
        }
      ];

      const mockAcquiringEntity: Partial<AcquiringEntityModelDocument> = {
        name: 'Test Company',
        address1: '123 Main St',
        city: 'Test City',
        country: 'United States',
        state: 'CA',
        zip: '12345',
        phone: ['1234567890'],
        registrationNumber: 'REG123',
        websites: mockWebsites,
        tradeOverInternet: true,
        isAllWebsitesClassified: true,
        taxId: 'TAX123',
        businessCategoryVmss: BusinessCategoryVmss.MERCHANT,
        principalsIds: mockPrincipals as any
      };

      (countryFormat.getTwoLetterCodeFromFullName as jest.Mock).mockReturnValue('US');

      const mockResponse = {
        possibleMatches: {
          matchedRecords: [
            {
              acquirerBID: '12345678',
              acquirerCountryOrRegion: 'US',
              terminatedRecord: {
                address: {
                  city: 'Test City',
                  countryOrRegion: 'US',
                  streetAddress: '123 Main St'
                },
                businessPhoneNumbers: ['1234567890'],
                cardAcceptorIDs: ['123'],
                category: '0',
                contractEndDate: '2023-01-01',
                contractStartDate: '2022-01-01',
                DBAName: 'Test Company',
                incorportationStatus: '1',
                primaryListingReason: 'Test Reason',
                principals: [
                  {
                    name: 'John Doe',
                    businessPhoneNumber: '1234567890'
                  }
                ],
                tradeInternationally: false,
                tradeOverInternet: true
              },
              terminatedRecordMatch: {
                addressMatch: {
                  city: 'E',
                  countryOrRegion: 'E',
                  streetAddress: 'E'
                },
                businessPhoneNumbers: 'E',
                DBAName: 'E'
              },
              terminatedRefID: 'ref-123'
            }
          ],
          totalCount: 1
        },
        searchRequestRef: {
          acquirerBID: '12345678',
          acquirerCountryOrRegion: 'US',
          terminatedRecordSearchCriteria: {
            address: {
              city: 'Test City',
              countryOrRegion: 'US',
              streetAddress: '123 Main St'
            },
            businessPhoneNumbers: ['1234567890'],
            category: '0',
            DBAName: 'Test Company',
            principals: [
              {
                name: 'John Doe',
                businessPhoneNumber: '1234567890'
              }
            ],
            tradeOverInternet: true
          }
        }
      };

      (checkAcquiringEntityRequest as jest.Mock).mockResolvedValue(mockResponse);

      // Act
      const result = await vmssAcquiringEntityMatchWithDescriptionInformation(
        mockAcquiringEntity as AcquiringEntityModelDocument
      );

      // Assert
      expect(countryFormat.getTwoLetterCodeFromFullName).toHaveBeenCalledWith('United States');
      expect(checkAcquiringEntityRequest).toHaveBeenCalledTimes(2); // Once for each website
      expect(result).toHaveLength(2);
      expect(result[0].response).toEqual(mockResponse);
      expect(result[0].url).toEqual('https://example.com');
    });

    it('should throw error when required fields are missing', async () => {
      // Arrange
      const mockAcquiringEntity: Partial<AcquiringEntityModelDocument> = {
        name: 'Test Company',
        // Missing address1, city, etc.
        country: 'United States',
        phone: ['1234567890'],
        registrationNumber: 'REG123'
      };

      (countryFormat.getTwoLetterCodeFromFullName as jest.Mock).mockReturnValue('US');

      // Act & Assert
      await expect(
        vmssAcquiringEntityMatchWithDescriptionInformation(mockAcquiringEntity as AcquiringEntityModelDocument)
      ).rejects.toThrow(CustomError);
    });

    it('should throw error when country region is not found', async () => {
      // Arrange
      // Mock principal data
      const mockPrincipals = [
        {
          _id: 'principal-1',
          firstName: 'John',
          lastName: 'Doe',
          phone: '1234567890',
          address1: '123 Main St',
          city: 'Test City',
          country: 'United States',
          state: 'CA',
          zip: '12345',
          email: '<EMAIL>'
        }
      ];

      const mockAcquiringEntity: Partial<AcquiringEntityModelDocument> = {
        name: 'Test Company',
        address1: '123 Main St',
        city: 'Test City',
        country: 'Unknown Country',
        state: 'CA',
        zip: '12345',
        phone: ['1234567890'],
        registrationNumber: 'REG123',
        businessCategoryVmss: BusinessCategoryVmss.MERCHANT,
        principalsIds: mockPrincipals as any,
        tradeOverInternet: false, // Set to false to avoid websites validation
        websites: [{ url: 'https://example.com', mccClassification: { mcc: '1234' } }], // Add websites anyway
        isAllWebsitesClassified: true
      };

      (countryFormat.getTwoLetterCodeFromFullName as jest.Mock).mockReturnValue(null);

      // Act & Assert
      await expect(
        vmssAcquiringEntityMatchWithDescriptionInformation(mockAcquiringEntity as AcquiringEntityModelDocument)
      ).rejects.toThrow('Country region not found');
    });

    it('should throw error when websites are not provided', async () => {
      // Arrange
      // Mock principal data
      const mockPrincipals = [
        {
          _id: 'principal-1',
          firstName: 'John',
          lastName: 'Doe',
          phone: '1234567890',
          address1: '123 Main St',
          city: 'Test City',
          country: 'United States',
          state: 'CA',
          zip: '12345',
          email: '<EMAIL>'
        }
      ];

      const mockAcquiringEntity: Partial<AcquiringEntityModelDocument> = {
        name: 'Test Company',
        address1: '123 Main St',
        city: 'Test City',
        country: 'United States',
        state: 'CA',
        zip: '12345',
        phone: ['1234567890'],
        registrationNumber: 'REG123',
        tradeOverInternet: true,
        websites: [], // Empty websites array
        isAllWebsitesClassified: true,
        businessCategoryVmss: BusinessCategoryVmss.MERCHANT,
        principalsIds: mockPrincipals as any
      };

      (countryFormat.getTwoLetterCodeFromFullName as jest.Mock).mockReturnValue('US');

      // Act & Assert
      await expect(
        vmssAcquiringEntityMatchWithDescriptionInformation(mockAcquiringEntity as AcquiringEntityModelDocument)
      ).rejects.toThrow('Websites not found!');
    });

    it('should throw error when not all websites are classified', async () => {
      // Arrange
      const mockWebsites = [{ url: 'https://example.com', mccClassification: { mcc: '1234' } }];

      // Mock principal data
      const mockPrincipals = [
        {
          _id: 'principal-1',
          firstName: 'John',
          lastName: 'Doe',
          phone: '1234567890',
          address1: '123 Main St',
          city: 'Test City',
          country: 'United States',
          state: 'CA',
          zip: '12345',
          email: '<EMAIL>'
        }
      ];

      const mockAcquiringEntity: Partial<AcquiringEntityModelDocument> = {
        name: 'Test Company',
        address1: '123 Main St',
        city: 'Test City',
        country: 'United States',
        state: 'CA',
        zip: '12345',
        phone: ['1234567890'],
        registrationNumber: 'REG123',
        tradeOverInternet: true,
        websites: mockWebsites,
        isAllWebsitesClassified: false, // Not all websites classified
        businessCategoryVmss: BusinessCategoryVmss.MERCHANT,
        principalsIds: mockPrincipals as any
      };

      (countryFormat.getTwoLetterCodeFromFullName as jest.Mock).mockReturnValue('US');

      // Act & Assert
      await expect(
        vmssAcquiringEntityMatchWithDescriptionInformation(mockAcquiringEntity as AcquiringEntityModelDocument)
      ).rejects.toThrow('Not all websites are classified!');
    });

    it('should handle API errors for individual websites', async () => {
      // Arrange
      const mockWebsites = [
        { url: 'https://example.com', mccClassification: { mcc: '1234' } },
        { url: 'https://error.com', mccClassification: { mcc: '5678' } }
      ];

      // Mock principal data
      const mockPrincipals = [
        {
          _id: 'principal-1',
          firstName: 'John',
          lastName: 'Doe',
          phone: '1234567890',
          address1: '123 Main St',
          city: 'Test City',
          country: 'United States',
          state: 'CA',
          zip: '12345',
          email: '<EMAIL>'
        }
      ];

      const mockAcquiringEntity: Partial<AcquiringEntityModelDocument> = {
        name: 'Test Company',
        address1: '123 Main St',
        city: 'Test City',
        country: 'United States',
        state: 'CA',
        zip: '12345',
        phone: ['1234567890'],
        registrationNumber: 'REG123',
        tradeOverInternet: true,
        websites: mockWebsites,
        isAllWebsitesClassified: true,
        businessCategoryVmss: BusinessCategoryVmss.MERCHANT,
        principalsIds: mockPrincipals as any
      };

      (countryFormat.getTwoLetterCodeFromFullName as jest.Mock).mockReturnValue('US');

      const mockResponse = {
        possibleMatches: {
          matchedRecords: [
            {
              acquirerBID: '12345678',
              acquirerCountryOrRegion: 'US',
              terminatedRecord: {
                address: {
                  city: 'Test City',
                  countryOrRegion: 'US',
                  streetAddress: '123 Main St'
                },
                businessPhoneNumbers: ['1234567890'],
                cardAcceptorIDs: ['123'],
                category: '0',
                contractEndDate: '2023-01-01',
                contractStartDate: '2022-01-01',
                DBAName: 'Test Company',
                incorportationStatus: '1',
                primaryListingReason: 'Test Reason',
                principals: [
                  {
                    name: 'John Doe',
                    businessPhoneNumber: '1234567890'
                  }
                ],
                tradeInternationally: false,
                tradeOverInternet: true
              },
              terminatedRecordMatch: {
                addressMatch: {
                  city: 'E',
                  countryOrRegion: 'E',
                  streetAddress: 'E'
                },
                businessPhoneNumbers: 'E',
                DBAName: 'E'
              },
              terminatedRefID: 'ref-123'
            }
          ],
          totalCount: 1
        },
        searchRequestRef: {
          acquirerBID: '12345678',
          acquirerCountryOrRegion: 'US',
          terminatedRecordSearchCriteria: {
            address: {
              city: 'Test City',
              countryOrRegion: 'US',
              streetAddress: '123 Main St'
            },
            businessPhoneNumbers: ['1234567890'],
            category: '0',
            DBAName: 'Test Company',
            principals: [
              {
                name: 'John Doe',
                businessPhoneNumber: '1234567890'
              }
            ],
            tradeOverInternet: true
          }
        }
      };

      const mockError = new Error('API error for second website');

      (checkAcquiringEntityRequest as jest.Mock).mockResolvedValueOnce(mockResponse).mockRejectedValueOnce(mockError);

      // Act
      const result = await vmssAcquiringEntityMatchWithDescriptionInformation(
        mockAcquiringEntity as AcquiringEntityModelDocument
      );

      // Assert
      expect(checkAcquiringEntityRequest).toHaveBeenCalledTimes(2);
      expect(result).toHaveLength(2);
      expect(result[0].response).toEqual(mockResponse);
      expect(result[0].url).toEqual('https://example.com');
      expect(result[1].error).toEqual(mockError.message);
      expect(result[1].url).toEqual('https://error.com');
    });
  });

  describe('getAcquiringEntityMatchInVisaVmssDashboard', () => {
    it('should get acquiring entity match data for dashboard', async () => {
      // Arrange
      const mockAcquiringEntityId = '507f1f77bcf86cd799439011'; // Valid MongoDB ObjectId format
      const mockPageLength = 10;
      const mockPageOffset = 0;

      // Mock principal data
      const mockPrincipals = [
        {
          _id: 'principal-1',
          firstName: 'John',
          lastName: 'Doe',
          phone: '1234567890',
          email: '<EMAIL>'
        }
      ];

      const mockVisaMatchDataItem: VisaMatchDataItem = {
        url: 'https://example.com',
        isMatch: true,
        visaTotalCountMatches: 1,
        visaResultData: {
          possibleMatches: {
            matchedRecords: [
              {
                acquirerBID: '12345678',
                acquirerCountryOrRegion: 'US',
                terminatedRecord: {
                  address: {
                    city: 'Test City',
                    countryOrRegion: 'US',
                    streetAddress: '123 Main St'
                  },
                  businessPhoneNumbers: ['1234567890'],
                  cardAcceptorIDs: ['123'],
                  category: '0',
                  contractEndDate: '2023-01-01',
                  contractStartDate: '2022-01-01',
                  DBAName: 'Test Company',
                  incorportationStatus: '1',
                  primaryListingReason: 'Test Reason',
                  principals: [
                    {
                      name: 'John Doe',
                      businessPhoneNumber: '1234567890'
                    }
                  ],
                  tradeInternationally: false,
                  tradeOverInternet: true
                },
                terminatedRecordMatch: {
                  addressMatch: {
                    city: 'E',
                    countryOrRegion: 'E',
                    streetAddress: 'E'
                  },
                  businessPhoneNumbers: 'E',
                  DBAName: 'E'
                },
                terminatedRefID: 'ref-123'
              }
            ],
            totalCount: 1
          }
        }
      };

      const mockVisaMatchData: VisaMatchData = {
        _id: 'match-1',
        data: [mockVisaMatchDataItem],
        requestMadeToCheckMerchant: 1,
        failedRequest: 0,
        isThereAnyMatch: true
      };

      const mockAcquiringEntity: Partial<AcquiringEntityModelDocument> = {
        _id: new Types.ObjectId(mockAcquiringEntityId),
        name: 'Test Company',
        address1: '123 Main St',
        city: 'Test City',
        country: 'United States',
        state: 'CA',
        zip: '12345',
        phone: ['1234567890'],
        registrationNumber: 'REG123',
        taxId: 'TAX123',
        websites: [{ url: 'https://example.com' }],
        principalsIds: mockPrincipals as any,
        visaMatchData: [mockVisaMatchData] as any,
        businessCategoryVmss: BusinessCategoryVmss.MERCHANT
      };

      (getAcquiringEntityById as jest.Mock).mockResolvedValue(mockAcquiringEntity);
      (countryFormat.getTwoLetterCodeFromFullName as jest.Mock).mockReturnValue('US');

      // Act
      const result = await getAcquiringEntityMatchInVisaVmssDashboard(
        mockAcquiringEntityId,
        mockPageLength,
        mockPageOffset
      );

      // Assert
      expect(getAcquiringEntityById).toHaveBeenCalledWith(mockAcquiringEntityId);
      expect(result).toHaveProperty('docs');
      expect(result).toHaveProperty('columnsData');
      expect(result).toHaveProperty('totalRequests', 1);
      expect(result).toHaveProperty('failedRequests', 0);
    });

    it('should throw error when acquiring entity is not found', async () => {
      // Arrange
      const mockAcquiringEntityId = 'nonexistent-id';
      const mockPageLength = 10;
      const mockPageOffset = 0;

      (getAcquiringEntityById as jest.Mock).mockResolvedValue(null);

      // Act & Assert
      await expect(
        getAcquiringEntityMatchInVisaVmssDashboard(mockAcquiringEntityId, mockPageLength, mockPageOffset)
      ).rejects.toThrow(`Acquiring entity with id: ${mockAcquiringEntityId} not found`);
    });

    it('should throw error when entity does not have visa match data', async () => {
      // Arrange
      const mockAcquiringEntityId = '507f1f77bcf86cd799439011'; // Valid MongoDB ObjectId format
      const mockPageLength = 10;
      const mockPageOffset = 0;

      const mockAcquiringEntity: Partial<AcquiringEntityModelDocument> = {
        _id: new Types.ObjectId(mockAcquiringEntityId),
        name: 'Test Company'
        // No visaMatchData
      };

      (getAcquiringEntityById as jest.Mock).mockResolvedValue(mockAcquiringEntity);

      // Act & Assert
      await expect(
        getAcquiringEntityMatchInVisaVmssDashboard(mockAcquiringEntityId, mockPageLength, mockPageOffset)
      ).rejects.toThrow('This entity does not pass Visa Vmss screening!');
    });

    it('should throw error when visa match data has error message', async () => {
      // Arrange
      const mockAcquiringEntityId = '507f1f77bcf86cd799439011'; // Valid MongoDB ObjectId format
      const mockPageLength = 10;
      const mockPageOffset = 0;

      const mockVisaMatchData: VisaMatchData = {
        _id: 'match-1',
        errorMessageRequest: 'API Error',
        requestMadeToCheckMerchant: 1,
        failedRequest: 1,
        isThereAnyMatch: false
      };

      const mockAcquiringEntity: Partial<AcquiringEntityModelDocument> = {
        _id: new Types.ObjectId(mockAcquiringEntityId),
        name: 'Test Company',
        visaMatchData: [mockVisaMatchData] as any
      };

      (getAcquiringEntityById as jest.Mock).mockResolvedValue(mockAcquiringEntity);

      // Act & Assert
      await expect(
        getAcquiringEntityMatchInVisaVmssDashboard(mockAcquiringEntityId, mockPageLength, mockPageOffset)
      ).rejects.toThrow('Failed to make Visa Vmss check because of error: API Error');
    });

    it('should handle case when no matches are found', async () => {
      // Arrange
      const mockAcquiringEntityId = '507f1f77bcf86cd799439011'; // Valid MongoDB ObjectId format
      const mockPageLength = 10;
      const mockPageOffset = 0;

      // Create a mock VisaMatchDataItem with no matches
      const mockVisaMatchDataItem: VisaMatchDataItem = {
        url: 'https://example.com',
        isMatch: false,
        visaTotalCountMatches: 0,
        visaResultData: {
          possibleMatches: {
            matchedRecords: [] // Empty matched records
          }
        }
      };

      const mockVisaMatchData: VisaMatchData = {
        _id: 'match-1',
        data: [mockVisaMatchDataItem], // Include the item but with no matches
        requestMadeToCheckMerchant: 1,
        failedRequest: 0,
        isThereAnyMatch: false
      };

      const mockAcquiringEntity: Partial<AcquiringEntityModelDocument> = {
        _id: new Types.ObjectId(mockAcquiringEntityId),
        name: 'Test Company',
        visaMatchData: [mockVisaMatchData] as any
      };

      (getAcquiringEntityById as jest.Mock).mockResolvedValue(mockAcquiringEntity);
      (countryFormat.getTwoLetterCodeFromFullName as jest.Mock).mockReturnValue('US');

      // Act
      const result = await getAcquiringEntityMatchInVisaVmssDashboard(
        mockAcquiringEntityId,
        mockPageLength,
        mockPageOffset
      );

      // Assert
      expect(result).toHaveProperty('docs');
      expect(result).toHaveProperty('columnsData');
      expect(result).toHaveProperty('totalRequests', 1);
      expect(result).toHaveProperty('failedRequests', 0);
    });
  });
});
