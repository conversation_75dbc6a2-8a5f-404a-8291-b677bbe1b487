import mongoose from 'mongoose';
import { AcquiringEntity } from '../../../interfaces/acquiringEntityInterface';
import AcquiringEntityModel from '../../../models/mongo/acquiringEntity';
import * as principalService from '../../principalService';
import * as sumsubService from '../../sumsubService';
import * as mccClassificationServer from '../../mccClassificationServer';
import { CustomError } from '../../../classes/CustomError';
import { Principal } from '../../../interfaces/principalInterface';
import { BusinessCategoryVmss } from '../../../enums/businessCategory';
import {
  createAcquiringEntity,
  getAcquiringEntitiesService,
  getAcquiringEntityById,
  checkAcquiringEntityByRegistrationNumber,
  createAcquiringEntityWithPrincipals,
  manualApproveAcquireMastercard,
  getAcquiringEntityWithAllProperties,
  createAcquiringEntityInSumsubApplicant,
  acquireEntityClassificationUrls
} from '../../acquiringEntityService';
import { MCCClassification } from '../../../interfaces/mccInterface';
import { MastercardMatchData } from '../../../interfaces/mastercardMatchProInterfaces';
import { ManualApproveAcquire } from '../../../interfaces/acquiringEntityBodyRequestInterface';
import * as countryFormat from '../../../utils/countryFormat';

// Mock the dependencies
jest.mock('../../../models/mongo/acquiringEntity');
jest.mock('../../principalService');
jest.mock('../../visaVmssService');
jest.mock('../../mastercardService');
jest.mock('../../sumsubService');
jest.mock('../../mccClassificationServer');
jest.mock('../../../utils/countryFormat');
jest.mock('../../../utils/logger', () => ({
  acquiringEntityLogger: {
    error: jest.fn(),
    info: jest.fn()
  },
  mastercardLogger: {
    error: jest.fn(),
    info: jest.fn()
  },
  visaVmssLogger: {
    error: jest.fn(),
    info: jest.fn()
  },
  sumsubLogger: {
    error: jest.fn(),
    info: jest.fn()
  }
}));

describe('Acquiring Entity Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('createAcquiringEntity', () => {
    it('should create an acquiring entity', async () => {
      // Arrange
      const mockAcquiringEntityData: AcquiringEntity = {
        name: 'Test Company',
        registrationNumber: '12345'
      };

      const mockCreatedEntity = {
        _id: new mongoose.Types.ObjectId(),
        ...mockAcquiringEntityData
      };

      (AcquiringEntityModel.create as jest.Mock).mockResolvedValue(mockCreatedEntity);

      // Act
      const result = await createAcquiringEntity(mockAcquiringEntityData);

      // Assert
      expect(AcquiringEntityModel.create).toHaveBeenCalledWith(mockAcquiringEntityData);
      expect(result).toEqual(mockCreatedEntity);
    });
  });

  describe('getAcquiringEntitiesService', () => {
    it('should get paginated acquiring entities', async () => {
      // Arrange
      const mockQuery = {};
      const mockOptions = { page: 1, limit: 10 };
      const mockPaginatedResult = {
        docs: [{ name: 'Test Company', registrationNumber: '12345' }],
        totalDocs: 1,
        limit: 10,
        page: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPrevPage: false
      };

      (AcquiringEntityModel.paginate as jest.Mock).mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await getAcquiringEntitiesService(mockQuery, mockOptions);

      // Assert
      expect(AcquiringEntityModel.paginate).toHaveBeenCalledWith(mockQuery, mockOptions);
      expect(result).toEqual(mockPaginatedResult);
    });
  });

  describe('getAcquiringEntityById', () => {
    it('should get an acquiring entity by ID', async () => {
      // Arrange
      const mockId = 'mockId';
      const mockEntity = {
        _id: mockId,
        name: 'Test Company',
        registrationNumber: '12345'
      };

      const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      // Act
      const result = await getAcquiringEntityById(mockId);

      // Assert
      expect(AcquiringEntityModel.findById).toHaveBeenCalledWith(mockId);
      expect(mockPopulate).toHaveBeenCalledWith('principalsIds');
      expect(result).toEqual(mockEntity);
    });

    it('should return null if entity not found', async () => {
      // Arrange
      const mockId = 'nonExistentId';
      const mockPopulate = jest.fn().mockResolvedValue(null);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      // Act
      const result = await getAcquiringEntityById(mockId);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('checkAcquiringEntityByRegistrationNumber', () => {
    it('should find an entity by registration number', async () => {
      // Arrange
      const mockRegNumber = '12345';
      const mockEntity = {
        name: 'Test Company',
        registrationNumber: mockRegNumber
      };

      (AcquiringEntityModel.findOne as jest.Mock).mockResolvedValue(mockEntity);

      // Act
      const result = await checkAcquiringEntityByRegistrationNumber(mockRegNumber);

      // Assert
      expect(AcquiringEntityModel.findOne).toHaveBeenCalledWith({ registrationNumber: mockRegNumber });
      expect(result).toEqual(mockEntity);
    });

    it('should return null if no entity found', async () => {
      // Arrange
      const mockRegNumber = 'nonExistentRegNumber';
      (AcquiringEntityModel.findOne as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await checkAcquiringEntityByRegistrationNumber(mockRegNumber);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('createAcquiringEntityWithPrincipals', () => {
    it('should create an acquiring entity with principals', async () => {
      // Arrange
      const mockPrincipal: Principal = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      };

      const mockAcquiringEntityData: AcquiringEntity = {
        name: 'Test Company',
        registrationNumber: '12345',
        principalRawData: [mockPrincipal]
      };

      const mockCreatedPrincipal = {
        _id: 'principalId',
        ...mockPrincipal
      };

      const mockCreatedEntity = {
        _id: new mongoose.Types.ObjectId(),
        ...mockAcquiringEntityData,
        principalsIds: [mockCreatedPrincipal._id]
      };

      (principalService.createPrincipal as jest.Mock).mockResolvedValue(mockCreatedPrincipal);
      (AcquiringEntityModel.findOne as jest.Mock).mockResolvedValue(null); // No existing entity
      (AcquiringEntityModel.create as jest.Mock).mockResolvedValue(mockCreatedEntity);

      // Mock the processAcquiringEntityScreening function
      jest.spyOn(global, 'setTimeout').mockImplementation((callback) => {
        return {} as any;
      });

      // Act
      const result = await createAcquiringEntityWithPrincipals(mockAcquiringEntityData);

      // Assert
      expect(principalService.createPrincipal).toHaveBeenCalledWith(mockPrincipal);
      expect(AcquiringEntityModel.create).toHaveBeenCalledWith({
        ...mockAcquiringEntityData,
        principalsIds: [mockCreatedPrincipal._id]
      });
      expect(result).toEqual({
        acquiringEntity: mockCreatedEntity,
        principals: [mockCreatedPrincipal]
      });
    });

    it('should throw an error if entity with registration number already exists', async () => {
      // Arrange
      const mockAcquiringEntityData: AcquiringEntity = {
        name: 'Test Company',
        registrationNumber: '12345'
      };

      const existingEntity = {
        ...mockAcquiringEntityData
      };

      (AcquiringEntityModel.findOne as jest.Mock).mockResolvedValue(existingEntity);

      // Act & Assert
      await expect(createAcquiringEntityWithPrincipals(mockAcquiringEntityData)).rejects.toThrow(CustomError);
    });

    it('should delete created principals if entity creation fails', async () => {
      // Arrange
      const mockPrincipal: Principal = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      };

      const mockAcquiringEntityData: AcquiringEntity = {
        name: 'Test Company',
        registrationNumber: '12345',
        principalRawData: [mockPrincipal]
      };

      const mockCreatedPrincipal = {
        _id: 'principalId',
        ...mockPrincipal
      };

      (principalService.createPrincipal as jest.Mock).mockResolvedValue(mockCreatedPrincipal);
      (AcquiringEntityModel.findOne as jest.Mock).mockResolvedValue(null); // No existing entity
      (AcquiringEntityModel.create as jest.Mock).mockRejectedValue(new Error('Creation failed'));
      (principalService.deletePrincipal as jest.Mock).mockResolvedValue(undefined);

      // Act & Assert
      await expect(createAcquiringEntityWithPrincipals(mockAcquiringEntityData)).rejects.toThrow('Creation failed');

      expect(principalService.deletePrincipal).toHaveBeenCalledWith(mockCreatedPrincipal._id);
    });
  });

  describe('getAcquiringEntityWithAllProperties', () => {
    it('should get an entity with all properties and default values', async () => {
      // Arrange
      const mockId = 'mockId';
      const mockEntity = {
        _id: mockId,
        name: 'Test Company',
        registrationNumber: '12345',
        toObject: jest.fn().mockReturnValue({
          _id: mockId,
          name: 'Test Company',
          registrationNumber: '12345'
        })
      };

      const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      const mockDefaultValues = {
        email: '',
        phone: [],
        country: '',
        state: '',
        city: '',
        zip: '',
        address1: '',
        address2: '',
        tradeOverInternet: false,
        websites: [],
        principalsIds: [],
        principalRawData: [],
        businessCategoryVmss: BusinessCategoryVmss.MERCHANT,
        taxId: ''
      };

      (principalService.getPrincipalDefaultValues as jest.Mock).mockReturnValue({
        firstName: '',
        middleName: '',
        lastName: '',
        email: '',
        phone: '',
        country: '',
        state: '',
        city: '',
        zip: '',
        address1: '',
        address2: '',
        dateOfBirth: '',
        passportNumber: '',
        driverLicenseNumber: ''
      });

      // Act
      const result = await getAcquiringEntityWithAllProperties(mockId);

      // Assert
      expect(AcquiringEntityModel.findById).toHaveBeenCalledWith(mockId);
      expect(result).toEqual({
        _id: mockId,
        name: 'Test Company',
        registrationNumber: '12345',
        ...mockDefaultValues,
        principalRawData: undefined,
        __v: undefined,
        createdAt: undefined,
        updatedAt: undefined,
        taxId: undefined,
        visaMatchData: undefined,
        mastercardMatchData: undefined
      });
    });

    it('should throw an error if entity not found', async () => {
      // Arrange
      const mockId = 'nonExistentId';
      const mockPopulate = jest.fn().mockResolvedValue(null);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      // Act & Assert
      await expect(getAcquiringEntityWithAllProperties(mockId)).rejects.toThrow(CustomError);
    });
  });

  describe('manualApproveAcquireMastercard', () => {
    it('should approve a mastercard match', async () => {
      // Arrange
      const mockData: ManualApproveAcquire = {
        acquiringEntityId: 'entityId',
        typeOfAction: 'approve',
        matchDataId: 'matchId'
      };

      const mockMatchData: MastercardMatchData = {
        _id: 'matchId',
        data: [],
        requestMadeToCheckMerchant: 1,
        failedRequest: 0,
        isThereAnyMatch: false
      };

      const mockEntity = {
        _id: 'entityId',
        mastercardMatchData: [mockMatchData],
        save: jest.fn().mockResolvedValue(true)
      };

      const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      // Act
      const result = await manualApproveAcquireMastercard(mockData);

      // Assert
      expect(mockEntity.mastercardMatchData[0].resolvedMastercardMatch).toBe(true);
      expect(mockEntity.save).toHaveBeenCalled();
      expect(result).toEqual(mockEntity);
    });

    it('should reject a mastercard match', async () => {
      // Arrange
      const mockData: ManualApproveAcquire = {
        acquiringEntityId: 'entityId',
        typeOfAction: 'reject',
        matchDataId: 'matchId'
      };

      const mockMatchData: MastercardMatchData = {
        _id: 'matchId',
        data: [],
        requestMadeToCheckMerchant: 1,
        failedRequest: 0,
        isThereAnyMatch: false
      };

      const mockEntity = {
        _id: 'entityId',
        mastercardMatchData: [mockMatchData],
        save: jest.fn().mockResolvedValue(true)
      };

      const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      // Act
      const result = await manualApproveAcquireMastercard(mockData);

      // Assert
      expect(mockEntity.mastercardMatchData[0].resolvedMastercardMatch).toBe(false);
      expect(mockEntity.save).toHaveBeenCalled();
      expect(result).toEqual(mockEntity);
    });

    it('should throw an error if entity not found', async () => {
      // Arrange
      const mockData: ManualApproveAcquire = {
        acquiringEntityId: 'nonExistentId',
        typeOfAction: 'approve',
        matchDataId: 'matchId'
      };

      const mockPopulate = jest.fn().mockResolvedValue(null);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      // Act & Assert
      await expect(manualApproveAcquireMastercard(mockData)).rejects.toThrow(CustomError);
    });
  });

  describe('acquireEntityClassificationUrls', () => {
    it('should classify website URLs for an entity', async () => {
      // Arrange
      const mockId = 'entityId';
      const mockWebsite = {
        url: 'https://example.com',
        mccClassification: {}
      };

      const mockEntity = {
        _id: mockId,
        websites: [mockWebsite],
        isAllWebsitesClassified: false,
        save: jest.fn().mockResolvedValue(true)
      };

      const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      const mockMccClassification: MCCClassification = {
        mcc: '1234',
        certainty: 0.9,
        description: 'Test MCC'
      };

      (mccClassificationServer.getMccClassification as jest.Mock).mockResolvedValue(mockMccClassification);

      // Act
      const result = await acquireEntityClassificationUrls(mockId);

      // Assert
      expect(mccClassificationServer.getMccClassification).toHaveBeenCalledWith('https://example.com');
      expect(mockEntity.websites[0].mccClassification).toEqual({
        url: 'https://example.com',
        mcc: '1234',
        certainty: 0.9,
        description: 'Test MCC',
        errorMessage: undefined,
        retryCount: 1
      });
      expect(mockEntity.isAllWebsitesClassified).toBe(true);
      expect(mockEntity.save).toHaveBeenCalled();
      expect(result).toEqual(mockEntity);
    });

    it('should handle errors during classification', async () => {
      // Arrange
      const mockId = 'entityId';
      const mockWebsite = {
        url: 'https://example.com',
        mccClassification: {}
      };

      const mockEntity = {
        _id: mockId,
        websites: [mockWebsite],
        isAllWebsitesClassified: false,
        save: jest.fn().mockResolvedValue(true)
      };

      const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      (mccClassificationServer.getMccClassification as jest.Mock).mockRejectedValue(new Error('Classification failed'));

      // Act
      const result = await acquireEntityClassificationUrls(mockId);

      // Assert
      expect(mccClassificationServer.getMccClassification).toHaveBeenCalledWith('https://example.com');
      expect(mockEntity.websites[0].mccClassification).toEqual({
        url: 'https://example.com',
        errorMessage: 'Classification failed',
        retryCount: 1
      });
      expect(mockEntity.isAllWebsitesClassified).toBe(false);
      expect(mockEntity.save).toHaveBeenCalled();
      expect(result).toEqual(mockEntity);
    });

    it('should throw an error if entity not found', async () => {
      // Arrange
      const mockId = 'nonExistentId';
      const mockPopulate = jest.fn().mockResolvedValue(null);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      // Act & Assert
      await expect(acquireEntityClassificationUrls(mockId)).rejects.toThrow();
    });
  });

  describe('createAcquiringEntityInSumsubApplicant', () => {
    it('should create an entity in Sumsub', async () => {
      // Arrange
      const mockId = 'entityId';
      const mockEntity = {
        _id: mockId,
        name: 'Test Company',
        registrationNumber: '12345',
        country: 'United States',
        sumsub: {},
        save: jest.fn().mockResolvedValue(true)
      };

      const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      (countryFormat.getThreeLetterCodeFromFullName as jest.Mock).mockReturnValue('USA');

      const mockSumsubResponse = {
        data: {
          id: 'sumsubId'
        }
      };

      (sumsubService.createApplicant as jest.Mock).mockResolvedValue(mockSumsubResponse);
      (sumsubService.rumAMLApplicantCheck as jest.Mock).mockResolvedValue(true);

      // Act
      const result = await createAcquiringEntityInSumsubApplicant(mockId);

      // Assert
      expect(countryFormat.getThreeLetterCodeFromFullName).toHaveBeenCalledWith('United States');
      expect(sumsubService.createApplicant).toHaveBeenCalled();
      expect(mockEntity.sumsub).toEqual({
        sumsubId: 'sumsubId',
        createApplicantError: undefined
      });
      expect(sumsubService.rumAMLApplicantCheck).toHaveBeenCalledWith('sumsubId');
      expect(mockEntity.save).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should handle errors during Sumsub creation', async () => {
      // Arrange
      const mockId = 'entityId';
      const mockEntity = {
        _id: mockId,
        name: 'Test Company',
        registrationNumber: '12345',
        country: 'United States',
        sumsub: {},
        save: jest.fn().mockResolvedValue(true)
      };

      const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
      (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
        populate: mockPopulate
      });

      (countryFormat.getThreeLetterCodeFromFullName as jest.Mock).mockReturnValue('USA');

      (sumsubService.createApplicant as jest.Mock).mockRejectedValue(new Error('Creation failed'));

      // Act
      const result = await createAcquiringEntityInSumsubApplicant(mockId);

      // Assert
      expect(mockEntity.sumsub).toEqual({
        createApplicantError: 'Creation failed'
      });
      expect(mockEntity.save).toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });
});
