import { CustomError } from '../../../classes/CustomError';
import AcquiringEntityModel from '../../../models/mongo/acquiringEntity';
import { manualApproveAcquireMastercardByRefNumber, getAcquiringEntityById } from '../../acquiringEntityService';
import { ManualApproveAcquireByRefNumber } from '../../../interfaces/acquiringEntityBodyRequestInterface';
import { MastercardMatchData, MastercardMatchDataItem } from '../../../interfaces/mastercardMatchProInterfaces';

// Mock dependencies
jest.mock('../../../models/mongo/acquiringEntity');
jest.mock('../../../utils/logger', () => ({
  acquiringEntityLogger: {
    error: jest.fn(),
    info: jest.fn()
  },
  mastercardLogger: {
    error: jest.fn(),
    info: jest.fn()
  }
}));

// Mock the AcquiringEntityModel.findById().populate() chain
jest.mock('../../../models/mongo/acquiringEntity', () => ({
  findById: jest.fn().mockReturnValue({
    populate: jest.fn()
  })
}));

describe('manualApproveAcquireMastercardByRefNumber', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should approve a mastercard match by reference number', async () => {
    // Arrange
    const mockRefNumber = 'ref-123';
    const mockData: ManualApproveAcquireByRefNumber = {
      acquiringEntityId: 'entityId',
      typeOfAction: 'approve',
      mastercardRefNumberMatched: mockRefNumber
    };

    const mockMatchDataItem: MastercardMatchDataItem = {
      url: 'https://example.com',
      isMatch: true,
      mastercardTotalCountMatches: 1,
      mastercardRefNumberMatched: mockRefNumber
    };

    const mockMatchData: MastercardMatchData = {
      _id: 'matchId',
      data: [mockMatchDataItem],
      requestMadeToCheckMerchant: 1,
      failedRequest: 0,
      isThereAnyMatch: true
    };

    const mockEntity = {
      _id: 'entityId',
      mastercardMatchData: [mockMatchData],
      save: jest.fn().mockResolvedValue(true)
    };

    const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
    (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
      populate: mockPopulate
    });

    // Act
    const result = await manualApproveAcquireMastercardByRefNumber(mockData);

    // Assert
    expect(AcquiringEntityModel.findById).toHaveBeenCalledWith('entityId');
    expect(mockEntity.mastercardMatchData?.[0]?.data?.[0]?.resolvedMastercardMatch).toBe(true);
    expect(mockEntity.save).toHaveBeenCalled();
    expect(result).toEqual(mockEntity);
  });

  it('should reject a mastercard match by reference number', async () => {
    // Arrange
    const mockRefNumber = 'ref-123';
    const mockData: ManualApproveAcquireByRefNumber = {
      acquiringEntityId: 'entityId',
      typeOfAction: 'reject',
      mastercardRefNumberMatched: mockRefNumber
    };

    const mockMatchDataItem: MastercardMatchDataItem = {
      url: 'https://example.com',
      isMatch: true,
      mastercardTotalCountMatches: 1,
      mastercardRefNumberMatched: mockRefNumber
    };

    const mockMatchData: MastercardMatchData = {
      _id: 'matchId',
      data: [mockMatchDataItem],
      requestMadeToCheckMerchant: 1,
      failedRequest: 0,
      isThereAnyMatch: true
    };

    const mockEntity = {
      _id: 'entityId',
      mastercardMatchData: [mockMatchData],
      save: jest.fn().mockResolvedValue(true)
    };

    const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
    (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
      populate: mockPopulate
    });

    // Act
    const result = await manualApproveAcquireMastercardByRefNumber(mockData);

    // Assert
    expect(mockEntity.mastercardMatchData?.[0]?.data?.[0]?.resolvedMastercardMatch).toBe(false);
    expect(mockEntity.save).toHaveBeenCalled();
    expect(result).toEqual(mockEntity);
  });

  it('should throw an error if entity not found', async () => {
    // Arrange
    const mockData: ManualApproveAcquireByRefNumber = {
      acquiringEntityId: 'nonExistentId',
      typeOfAction: 'approve',
      mastercardRefNumberMatched: 'ref-123'
    };

    const mockPopulate = jest.fn().mockResolvedValue(null);
    (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
      populate: mockPopulate
    });

    // Act & Assert
    await expect(manualApproveAcquireMastercardByRefNumber(mockData)).rejects.toThrow(CustomError);
  });

  it('should throw an error if entity has no mastercard match data', async () => {
    // Arrange
    const mockData: ManualApproveAcquireByRefNumber = {
      acquiringEntityId: 'entityId',
      typeOfAction: 'approve',
      mastercardRefNumberMatched: 'ref-123'
    };

    const mockEntity = {
      _id: 'entityId',
      mastercardMatchData: null,
      save: jest.fn().mockResolvedValue(true)
    };

    const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
    (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
      populate: mockPopulate
    });

    // Act & Assert
    await expect(manualApproveAcquireMastercardByRefNumber(mockData)).rejects.toThrow(CustomError);
  });

  it('should throw an error if reference number not found', async () => {
    // Arrange
    const mockData: ManualApproveAcquireByRefNumber = {
      acquiringEntityId: 'entityId',
      typeOfAction: 'approve',
      mastercardRefNumberMatched: 'nonExistentRef'
    };

    const mockMatchDataItem: MastercardMatchDataItem = {
      url: 'https://example.com',
      isMatch: true,
      mastercardTotalCountMatches: 1,
      mastercardRefNumberMatched: 'different-ref'
    };

    const mockMatchData: MastercardMatchData = {
      _id: 'matchId',
      data: [mockMatchDataItem],
      requestMadeToCheckMerchant: 1,
      failedRequest: 0,
      isThereAnyMatch: true
    };

    const mockEntity = {
      _id: 'entityId',
      mastercardMatchData: [mockMatchData],
      save: jest.fn().mockResolvedValue(true)
    };

    const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
    (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
      populate: mockPopulate
    });

    // Act & Assert
    await expect(manualApproveAcquireMastercardByRefNumber(mockData)).rejects.toThrow(CustomError);
  });
});
