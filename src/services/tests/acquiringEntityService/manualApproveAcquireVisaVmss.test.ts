import mongoose from 'mongoose';
import { CustomError } from '../../../classes/CustomError';
import AcquiringEntityModel from '../../../models/mongo/acquiringEntity';
import { manualApproveAcquireVisaVmss } from '../../acquiringEntityService';
import { ManualApproveAcquire } from '../../../interfaces/acquiringEntityBodyRequestInterface';
import { VisaMatchData } from '../../../interfaces/visaVmssInterface';

// Mock dependencies
jest.mock('../../../models/mongo/acquiringEntity', () => ({
  findById: jest.fn().mockReturnValue({
    populate: jest.fn()
  })
}));

jest.mock('../../principalService');
jest.mock('../../../utils/logger', () => ({
  acquiringEntityLogger: {
    error: jest.fn(),
    info: jest.fn()
  },
  visaVmssLogger: {
    error: jest.fn(),
    info: jest.fn()
  }
}));

describe('manualApproveAcquireVisaVmss', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should approve a Visa VMSS match', async () => {
    // Arrange
    const mockMatchId = new mongoose.Types.ObjectId().toString();
    const mockData: ManualApproveAcquire = {
      acquiringEntityId: 'entityId',
      typeOfAction: 'approve',
      matchDataId: mockMatchId
    };

    const mockMatchData: VisaMatchData = {
      _id: mockMatchId,
      data: [],
      requestMadeToCheckMerchant: 1,
      failedRequest: 0,
      isThereAnyMatch: false
    };

    const mockEntity = {
      _id: 'entityId',
      name: 'Test Company',
      registrationNumber: '12345',
      visaMatchData: [mockMatchData],
      save: jest.fn().mockResolvedValue(true)
    };

    const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
    (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
      populate: mockPopulate
    });

    // Act
    const result = await manualApproveAcquireVisaVmss(mockData);

    // Assert
    expect(AcquiringEntityModel.findById).toHaveBeenCalledWith('entityId');
    expect(mockEntity.visaMatchData[0].resolvedVisaMatch).toBe(true);
    expect(mockEntity.save).toHaveBeenCalled();
    expect(result).toEqual(mockEntity);
  });

  it('should reject a Visa VMSS match', async () => {
    // Arrange
    const mockMatchId = new mongoose.Types.ObjectId().toString();
    const mockData: ManualApproveAcquire = {
      acquiringEntityId: 'entityId',
      typeOfAction: 'reject',
      matchDataId: mockMatchId
    };

    const mockMatchData: VisaMatchData = {
      _id: mockMatchId,
      data: [],
      requestMadeToCheckMerchant: 1,
      failedRequest: 0,
      isThereAnyMatch: false
    };

    const mockEntity = {
      _id: 'entityId',
      name: 'Test Company',
      registrationNumber: '12345',
      visaMatchData: [mockMatchData],
      save: jest.fn().mockResolvedValue(true)
    };

    const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
    (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
      populate: mockPopulate
    });

    // Act
    const result = await manualApproveAcquireVisaVmss(mockData);

    // Assert
    expect(mockEntity.visaMatchData[0].resolvedVisaMatch).toBe(false);
    expect(mockEntity.save).toHaveBeenCalled();
    expect(result).toEqual(mockEntity);
  });

  it('should throw an error if entity not found', async () => {
    // Arrange
    const mockData: ManualApproveAcquire = {
      acquiringEntityId: 'nonExistentId',
      typeOfAction: 'approve',
      matchDataId: 'matchId'
    };

    const mockPopulate = jest.fn().mockResolvedValue(null);
    (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
      populate: mockPopulate
    });

    // Act & Assert
    await expect(manualApproveAcquireVisaVmss(mockData)).rejects.toThrow(CustomError);
    expect(AcquiringEntityModel.findById).toHaveBeenCalledWith('nonExistentId');
  });

  it('should handle case when match data ID is not found', async () => {
    // Arrange
    const mockData: ManualApproveAcquire = {
      acquiringEntityId: 'entityId',
      typeOfAction: 'approve',
      matchDataId: 'nonExistentMatchId'
    };

    const mockMatchData: VisaMatchData = {
      _id: 'differentMatchId',
      data: [],
      requestMadeToCheckMerchant: 1,
      failedRequest: 0,
      isThereAnyMatch: false
    };

    const mockEntity = {
      _id: 'entityId',
      name: 'Test Company',
      registrationNumber: '12345',
      visaMatchData: [mockMatchData],
      save: jest.fn().mockResolvedValue(true)
    };

    const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
    (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
      populate: mockPopulate
    });

    // Act
    const result = await manualApproveAcquireVisaVmss(mockData);

    // Assert
    // The function should not modify any match data since the ID doesn't match
    expect(mockEntity.visaMatchData[0].resolvedVisaMatch).toBeUndefined();
    expect(mockEntity.save).toHaveBeenCalled();
    expect(result).toEqual(mockEntity);
  });

  it('should handle entity with no visa match data', async () => {
    // Arrange
    const mockData: ManualApproveAcquire = {
      acquiringEntityId: 'entityId',
      typeOfAction: 'approve',
      matchDataId: 'matchId'
    };

    const mockEntity = {
      _id: 'entityId',
      name: 'Test Company',
      registrationNumber: '12345',
      visaMatchData: undefined,
      save: jest.fn().mockResolvedValue(true)
    };

    const mockPopulate = jest.fn().mockResolvedValue(mockEntity);
    (AcquiringEntityModel.findById as jest.Mock).mockReturnValue({
      populate: mockPopulate
    });

    // Act
    const result = await manualApproveAcquireVisaVmss(mockData);

    // Assert
    // The function should not throw an error, just save the entity as is
    expect(mockEntity.save).toHaveBeenCalled();
    expect(result).toEqual(mockEntity);
  });
});
