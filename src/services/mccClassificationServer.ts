import axios, { Method } from 'axios';
import { MCC_SERVICE_CLASSIFIER_BASE_URL } from '../config/config';

function getOptions(
  method: Method,
  path: string = '',
  data: Record<string, any> = {},
  params: Record<string, any> = {}
) {
  const hostname = MCC_SERVICE_CLASSIFIER_BASE_URL;

  const config = {
    method: method.toUpperCase(),
    url: `${hostname}${path}`,
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 120000,
    ...(method.toUpperCase() !== 'GET' && { data }),
    params: params
  };

  return config;
}

export async function getMccClassification(url: string) {
  try {
    const options = getOptions('GET', `/api/classify?url=${url}`);
    const response = await axios(options);
    return response.data;
  } catch (error: any) {
    let message = error.message;

    if (error.response && error.response.data.message) {
      message = error.response.data.message;
    }
    throw new Error(message);
  }
}
