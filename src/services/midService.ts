import logger from '../utils/logger';
import { publishMidEvent } from '../kafka/publishers/onboarding';
import { AcquiringEntityModelDocument } from '../models/mongo/acquiringEntity';

export async function configureMidReference(
  acquiringEntity: AcquiringEntityModelDocument,
  parentMid: string,
  pspNumber: string
): Promise<void> {
  const entityType = acquiringEntity.entityType;
  const isMasterMid = (acquiringEntity.websites ?? [])?.length > 1;

  try {
    const message = {
      id: acquiringEntity._id,
      entityType: entityType,
      midToBeAttached: parentMid,
      pspNumber: pspNumber,
      isMasterMid: isMasterMid
    };

    await publishMidEvent('configureMid', message);
  } catch (error: any) {
    logger.error(`Error publishing mid event: ${error?.message ?? error}`);
    throw error;
  }
}
