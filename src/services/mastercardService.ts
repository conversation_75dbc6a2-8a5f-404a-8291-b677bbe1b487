import { AcquiringEntityModelDocument } from '../models/mongo/acquiringEntity';
import {
  MastercardMatchDataItem,
  MastercardMatchData,
  Principal,
  TerminationInquiryDTO
} from '../interfaces/mastercardMatchProInterfaces';
import { CustomError } from '../classes/CustomError';
import { getThreeLetterCodeFromFullName } from '../utils/countryFormat';
import { TerminationInquiryRequest } from '../interfaces/mastercardMatchProInterfaces';
import {
  mastercardMatchGetRetrievedHistoryTerminationRequest,
  mastercardMatchProCheckAcquiringEntityRequest
} from '../requests/ryvylMastercardMatchProRequests';
import { mastercardLogger } from '../utils/logger';
import { getAcquiringEntityById } from './acquiringEntityService';
import { getPaginationData } from '../utils/pagePagination';
import { getUniqueKeys, sortKeys, transformSpecialKeys } from '../utils/objectTranformation';
import { flattenObject } from '../utils/objectTranformation';
import { PrincipalModelDocument } from '../models/mongo/principal';
import { Types } from 'mongoose';
import { getLocalPhoneNumberWithCountryCode } from '../utils/phoneFormat';

// Function to check if an object has the required properties
function hasProperty(obj: any, path: string): boolean {
  return (
    path.split('.').reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined), obj) !== undefined
  );
}

function validateFields(entity: any, requiredFields: string[]): string[] {
  return requiredFields.filter((field) => {
    // Missing this fields because we need to add them to the acquiring entity manually
    const missTheFields = ['globalSearch'];
    if (missTheFields.includes(field)) {
      return false;
    }

    return !hasProperty(entity, field);
  });
}

export async function getRetrievedHistoryTermination(
  referenceNumber: string,
  page_length: number,
  page_offset: number
) {
  try {
    const response = await mastercardMatchGetRetrievedHistoryTerminationRequest(
      referenceNumber,
      page_length,
      page_offset
    );
    return response;
  } catch (error: any) {
    mastercardLogger.error(`Error in getRetrievedHistoryTermination: ${error.message}`);
    throw error;
  }
}

export async function acquiringEntityMatchPro(
  acquiringEntity: AcquiringEntityModelDocument,
  page_length: number,
  page_offset: number
) {
  const mastercardRequiredFields = ['name', 'address1', 'city', 'country', 'zip', 'phone'];

  const mastercardMatchProRequiredFieldsByRyvyl = [
    'name',
    'address1',
    'city',
    'country',
    'zip',
    'phone',
    'registrationNumber'
  ];

  if (acquiringEntity.tradeOverInternet === true) {
    if (!acquiringEntity.websites || acquiringEntity.websites.length <= 0) {
      mastercardMatchProRequiredFieldsByRyvyl.push('websites');
    }
  }

  const missingMastercardFields = validateFields(acquiringEntity, mastercardRequiredFields);
  const missingMastercardRyvylFields = validateFields(acquiringEntity, mastercardMatchProRequiredFieldsByRyvyl);

  const countryRegion = getThreeLetterCodeFromFullName(acquiringEntity.country as string);
  if (!countryRegion) {
    throw new CustomError('Country region not found', 400);
  }

  let state = null;
  if (countryRegion === 'USA' || countryRegion === 'CAN') {
    if (acquiringEntity.state === undefined || acquiringEntity.state === null) {
      missingMastercardFields.push('state');
    } else {
      state = acquiringEntity.state;
    }
  }

  const principals: Principal[] =
    acquiringEntity.principalsIds?.map((principal: any, index: number) => {
      if (!principal.firstName) {
        missingMastercardRyvylFields.push(`Missing required fields on ${index} principal field: firstName`);
      }

      if (!principal.lastName) {
        missingMastercardRyvylFields.push(`Missing required fields on ${index} principal field: lastName`);
      }
      if (!principal.phone) {
        missingMastercardRyvylFields.push(`Missing required fields on ${index} principal field: phone`);
      }

      if (!principal.address1) {
        missingMastercardRyvylFields.push(`Missing required fields on ${index} principal field: address1`);
      }

      if (!principal.city) {
        missingMastercardRyvylFields.push(`Missing required fields on ${index} principal field: city`);
      }

      if (!principal.country) {
        missingMastercardRyvylFields.push(`Missing required fields on ${index} principal field: country`);
      }

      const countryRegion = getThreeLetterCodeFromFullName(principal.country as string);
      if (!countryRegion) {
        missingMastercardRyvylFields.push(
          `Country region not found for principal: ${principal.firstName} ${principal.lastName} country: ${principal.country}`
        );
      }

      let state = null;

      if (countryRegion === 'USA' || countryRegion === 'CAN') {
        state = principal.state;

        if (!state) {
          missingMastercardRyvylFields.push(`Missing required fields on ${index} principal field: state`);
        }
      }

      return {
        firstName: principal.firstName,
        lastName: principal.lastName,
        ...(principal.phone && { phoneNumber: getLocalPhoneNumberWithCountryCode(principal.phone) }),
        ...(principal.middleName && { middleInitial: principal.middleName.charAt(0) }),
        ...(principal.email && { email: principal.email }),
        ...(principal.dateOfBirth && { dateOfBirth: principal.dateOfBirth }),
        ...(principal.nationalId && { nationalId: principal.nationalId }),
        address: {
          addressLineOne: principal.address1,
          city: principal.city,
          isOtherCity: 'Y',
          country: countryRegion,
          ...(state && { countrySubdivision: state }),
          postalCode: principal.zip,
          ...(principal.address2 && { addressLineTwo: principal.address2 })
        }
      };
    }) || [];
  if (missingMastercardFields.length > 0 || missingMastercardRyvylFields.length > 0) {
    throw new CustomError(
      ` ${JSON.stringify([...missingMastercardFields, ...missingMastercardRyvylFields], null, 2)}`,
      400
    );
  }

  if (principals.length <= 0) {
    throw new CustomError('Principals not found!', 400);
  }

  let merchantPhoneNumber: string | null = null;
  if (acquiringEntity.phone) {
    merchantPhoneNumber = acquiringEntity.phone[0];
  }

  const mastercardMatchProData: TerminationInquiryRequest = {
    terminationInquiryRequest: {
      merchant: {
        name: acquiringEntity.name,
        doingBusinessAsName: acquiringEntity.name,
        // merchantId: acquiringEntity.merchantId,
        // subMerchantId: acquiringEntity.subMerchantId,
        address: {
          addressLineOne: acquiringEntity.address1,
          city: acquiringEntity.city,
          isOtherCity: 'Y',
          country: countryRegion,
          postalCode: acquiringEntity.zip,
          ...(state && { countrySubdivision: state }),
          ...(acquiringEntity.address2 && { addressLineTwo: acquiringEntity.address2 })
        },
        ...(merchantPhoneNumber && { phoneNumber: getLocalPhoneNumberWithCountryCode(merchantPhoneNumber) }),
        ...(acquiringEntity.taxId && { nationalTaxId: acquiringEntity.taxId }),
        ...(acquiringEntity.taxId && { countrySubdivisionTaxId: acquiringEntity.taxId }),
        principals: principals,
        searchCriteria: {
          minPossibleMatchCount: '3'
        }
      }
    }
  };

  if (!acquiringEntity.websites || acquiringEntity.websites.length <= 0) {
    throw new CustomError('Websites not found!', 400);
  }

  if (acquiringEntity.isAllWebsitesClassified == false) {
    throw new CustomError('Not all websites are classified!', 400);
  }

  const mastercardMatchProDataArray: TerminationInquiryRequest[] = [];
  // const mastercardMatchProDataArray: { terminationInquiryRequest: TerminationInquiryRequest; url: string }[] = [];
  acquiringEntity.websites.forEach((website: any) => {
    if (website.url && website?.mccClassification?.mcc) {
      mastercardMatchProDataArray.push({
        ...mastercardMatchProData,
        terminationInquiryRequest: {
          ...mastercardMatchProData.terminationInquiryRequest,
          merchant: {
            ...mastercardMatchProData.terminationInquiryRequest.merchant,
            urls: [website.url],
            merchantCategory: website?.mccClassification?.mcc
          }
        }
      });
    }
  });

  const mastercardMatchProDataArrayResponse: {
    terminationInquiryResponse?: TerminationInquiryDTO;
    error?: any;
    url: string | undefined;
  }[] = [];
  for (const data of mastercardMatchProDataArray) {
    try {
      const response = await mastercardMatchProCheckAcquiringEntityRequest(data, page_length, page_offset);
      mastercardMatchProDataArrayResponse.push({
        terminationInquiryResponse: response.terminationInquiryResponse,
        url: data.terminationInquiryRequest?.merchant?.urls?.[0]
      });
    } catch (error) {
      mastercardMatchProDataArrayResponse.push({
        error: error,
        url: data.terminationInquiryRequest?.merchant?.urls?.[0]
      });
    }
  }

  return mastercardMatchProDataArrayResponse;
}

export async function getAcquiringEntityMatchInMastercardDashboard(
  acquiringEntityId: string,
  page_length: number,
  page_offset: number,
  mastercardRefNumberMatched: string
) {
  try {
    const acquiringEntity: AcquiringEntityModelDocument | null = await getAcquiringEntityById(acquiringEntityId);
    if (!acquiringEntity) {
      throw new CustomError(`Acquiring entity with id: ${acquiringEntityId} not found`, 404);
    }

    let response;
    try {
      response = await mastercardMatchGetRetrievedHistoryTerminationRequest(
        mastercardRefNumberMatched,
        page_length,
        page_offset
      );
    } catch (error: any) {
      mastercardLogger.error(`Error in getRetrievedHistoryTermination: ${error.message}`);
      throw new CustomError(`Mastercard API Error, please try again later!`, 400);
    }
    if (!response) {
      throw new CustomError(`Mastercard API Error, please try again later!`, 400);
    }

    const mappedAcquiringEntity = mapAcquiringEntityToMastercardResponse(
      acquiringEntity as AcquiringEntityModelDocument
    );

    let matchReferenceData: MastercardMatchDataItem | undefined = undefined;
    let lastMatch: Date | undefined = undefined;

    for (let i = (acquiringEntity.mastercardMatchData?.length as number) - 1; i >= 0; i--) {
      const match: MastercardMatchData | undefined = acquiringEntity.mastercardMatchData?.[i];
      const matchItem: MastercardMatchDataItem | undefined = match?.data?.find((item: MastercardMatchDataItem) => {
        return item.mastercardRefNumberMatched === mastercardRefNumberMatched;
      });
      if (matchItem) {
        matchReferenceData = matchItem;
        //@ts-ignore
        lastMatch = matchItem.createdAt;
        break;
      }
    }

    const totalMerchantMatches = matchReferenceData?.mastercardTotalCountMatches || 0;
    const matchDataArray =
      response.terminationInquiryHistory?.terminationInquiryResponse?.possibleMerchantMatches?.[0]?.terminatedMerchants;
    if (!matchDataArray) {
      throw new CustomError(`Mastercard Matches data in not provided!`, 400);
    }
    const paginationData = getPaginationData(totalMerchantMatches, page_length, page_offset + 1);
    //@ts-ignore
    matchDataArray.unshift({ merchant: mappedAcquiringEntity });
    const dataKeyValuePairs = matchDataArray.map((match: any) => {
      return {
        merchant: flattenObject(match?.merchant, '', ['principals', 'urls']),
        merchantMatch: match?.merchantMatch
      };
    });

    const transformedDataKeyValuePairs = dataKeyValuePairs.map((el: any) => {
      let item = el.merchant;
      for (let key in item) {
        if (item.hasOwnProperty(key)) {
          if (key === 'urls' || key === 'principals') {
            const result = transformSpecialKeys(key, item[key]);
            delete item[key];
            item = { ...item, ...result };
            continue;
          }
        }
      }
      return { ...item, merchantMatch: el.merchantMatch };
    });
    const arrayOfColumns = getUniqueKeys(transformedDataKeyValuePairs, ['merchantMatch', 'urlGroups']);

    const sortedColumns = sortKeys(arrayOfColumns as string[], [
      'name',
      'registrationNumber',
      'address',
      'phoneNumber'
    ]);

    return {
      ...paginationData,
      docs: transformedDataKeyValuePairs,
      columnsData: sortedColumns,
      lastMatch: lastMatch
    };
  } catch (error: any) {
    mastercardLogger.error(`Failed to check acquiring entity match in Mastercard: ${error.message}`);
    throw error;
  }
}

function mapAcquiringEntityToMastercardResponse(acquiringEntity: AcquiringEntityModelDocument) {
  let principals: any[] = [];
  if (acquiringEntity.principalsIds) {
    // @ts-ignore
    principals = acquiringEntity.principalsIds.map((principal: PrincipalModelDocument) => {
      return {
        firstName: principal.firstName,
        middleName: principal.middleName,
        lastName: principal.lastName,
        phoneNumber: principal.phone,
        email: principal.email,
        dateOfBirth: principal.dateOfBirth,
        address: {
          addressLineOne: principal.address1,
          addressLineTwo: principal.address2,
          city: principal.city,
          countrySubdivision: principal.state,
          postalCode: principal.zip,
          country: principal.country ? getThreeLetterCodeFromFullName(principal.country) : ''
        }
      };
    });
  }

  const data = {
    _id: (acquiringEntity._id as Types.ObjectId).toString(),
    name: acquiringEntity.name,
    registrationNumber: acquiringEntity.registrationNumber,
    address: {
      addressLineOne: acquiringEntity.address1,
      addressLineTwo: acquiringEntity.address2,
      city: acquiringEntity.city,
      countrySubdivision: acquiringEntity.state,
      postalCode: acquiringEntity.zip,
      country: acquiringEntity.country ? getThreeLetterCodeFromFullName(acquiringEntity.country) : ''
    },
    phoneNumber: acquiringEntity.phone?.join(', '),
    nationalTaxId: acquiringEntity.taxId,
    urls: acquiringEntity.websites,
    principals: principals
  };
  return data;
}
