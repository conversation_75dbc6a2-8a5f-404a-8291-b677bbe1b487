import { Start<PERSON>ronJob, EVERY_MINUTE } from '@submodules/ryvyl-commons/services/cronService';
import { acquiringEntityLogger } from '../utils/logger';
import { retryMccClassification } from './acquiringEntityService';

export async function startAcquiringEntityWebsiteRetryCronJobs() {
  StartCronJob(
    {
      name: 'Acquiring Entity Website Retry Cron Jobs',
      logger: acquiringEntityLogger,
      jobFunction: retryMccClassification
    },
    EVERY_MINUTE
  );
}
