import axios, { Method } from 'axios';
import { SUMSUB_RYVYL_SERVICE_BASE_URL } from '../config/config';
import {
  CreateApplicantRequest,
  GenerateExternalWebSDKLinkRequest,
  GenerateExternalWebSDKLinkResponse,
  LinkBeneficiaryResponse,
  LinkBeneficiaryToCompany
} from '../interfaces/sumsubApplicantInterface';

function getOptions(
  method: Method,
  path: string = '',
  data: Record<string, any> = {},
  params: Record<string, any> = {}
) {
  const hostname = SUMSUB_RYVYL_SERVICE_BASE_URL;
  if (!hostname) {
    throw new Error('SUMSUB_RYVYL_SERVICE_BASE_URL environment variables is not defined!');
  }

  const config = {
    method: method.toUpperCase(),
    url: `${hostname}${path}`,
    headers: {
      'Content-Type': 'application/json'
    },
    ...(method.toUpperCase() !== 'GET' && { data }),
    params: params
  };

  return config;
}

export async function createApplicant(levelName: string, data: CreateApplicantRequest) {
  const payload = {
    ...data,
    levelName
  };

  const options = getOptions('POST', `/create-sumsub-applicant`, payload);

  try {
    const transactions = await axios(options);

    return transactions.data;
  } catch (error: any) {
    let message = '';

    if (error?.response?.data?.messages) {
      message = error?.response?.data?.messages;
    } else if (error?.response?.data?.message) {
      message = error?.response?.data?.message;
    } else {
      message = `Failed to create applicant: ${error.message || 'Unknown error occurred'}`;
    }
    throw new Error(JSON.stringify(message, null, 2));
  }
}

export async function rumAMLApplicantCheck(sumsubId: string) {
  const payload = {
    sumsubId
  };
  const options = getOptions('POST', `/proxy/run-aml-check`, payload);

  try {
    await axios(options);
  } catch (error: any) {
    const messages = error?.response?.data?.messages;
    const message = error?.response?.data?.message;
    let returnMessage = '';

    if (messages) {
      returnMessage = `\n${messages.join('\n')}`;
    } else if (message) {
      returnMessage = message;
    } else {
      returnMessage = `Failed to run AML check: ${error.message || 'Unknown error occurred'}`;
    }
    throw new Error(JSON.stringify(returnMessage, null, 2));
  }
}

export async function generateExternalWebSDKLink(
  payload: GenerateExternalWebSDKLinkRequest
): Promise<GenerateExternalWebSDKLinkResponse> {
  const options = getOptions('POST', `/generate-external-web-sdk-link`, payload);

  try {
    const transactions = await axios(options);

    return transactions.data;
  } catch (error: any) {
    let message = '';

    if (error?.response?.data?.messages) {
      message = error?.response?.data?.messages;
    } else if (error?.response?.data?.message) {
      message = error?.response?.data?.message;
    } else {
      message = `Failed to create applicant: ${error.message || 'Unknown error occurred'}`;
    }
    throw new Error(JSON.stringify(message, null, 2));
  }
}

export async function linkBeneficiaryToCompany(payload: LinkBeneficiaryToCompany): Promise<LinkBeneficiaryResponse> {
  const options = getOptions('POST', `/link-beneficiary-to-company`, payload);

  try {
    const transactions = await axios(options);
    return transactions.data;
  } catch (error: any) {
    let message = '';

    if (error?.response?.data?.messages) {
      message = error?.response?.data?.messages;
    } else if (error?.response?.data?.message) {
      message = error?.response?.data?.message;
    } else {
      message = `Failed to create applicant: ${error.message || 'Unknown error occurred'}`;
    }
    throw new Error(JSON.stringify(message, null, 2));
  }
}
