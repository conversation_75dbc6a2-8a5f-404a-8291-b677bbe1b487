import fs from 'fs';
import path from 'path';
import handlebars from 'handlebars';
import puppeteer from 'puppeteer';
import { AcquiringEntity } from '../interfaces/acquiringEntityInterface';
import { acquiringEntityLogger } from '../utils/logger';

/**
 * Interface for PDF generation result
 */
export interface PDFGenerationResult {
  base64Content: string;
  email: string;
  filename: string;
}

/**
 * Register Handlebars helpers
 */
handlebars.registerHelper('add', function (value, addition) {
  return value + addition;
});

// Register equality comparison helper
handlebars.registerHelper('eq', function (arg1, arg2) {
  return arg1 === arg2;
});

// Register includes helper to check if a string contains a substring
handlebars.registerHelper('includes', function (str, substring) {
  if (typeof str !== 'string') return false;
  return str.includes(substring);
});

// Register a helper to check if an array has any items that include a substring
handlebars.registerHelper('anyIncludes', function (array, substring) {
  if (!Array.isArray(array)) return false;
  return array.some((item) => typeof item === 'string' && item.includes(substring));
});

// Register a helper to check if an array has any items that do NOT include a substring
handlebars.registerHelper('anyNotIncludes', function (array, substring) {
  if (!Array.isArray(array)) return false;
  return array.some((item) => typeof item === 'string' && !item.includes(substring));
});

/**
 * Generate HTML content for a Merchant Agreement using Handlebars template
 * @param acquiringEntity The acquiring entity data
 * @returns HTML string
 */
export function generateMerchantAgreementHTML(acquiringEntity: AcquiringEntity): string {
  try {
    // Read the HTML template
    const templatePath = path.join(process.cwd(), 'src/templates/merchantAgreement.hbs');
    const templateHtml = fs.readFileSync(templatePath, 'utf-8');

    // Compile template with Handlebars
    const template = handlebars.compile(templateHtml);
    const date = new Date();

    // Get logo path and convert to base64
    const logoPath = path.join(process.cwd(), 'src/assets/ryvyl-logo.jpeg');
    let logoBase64 = '';

    try {
      // Read the logo file as binary data and convert to base64
      const logoContent = fs.readFileSync(logoPath);
      logoBase64 = logoContent.toString('base64');
    } catch (error) {
      acquiringEntityLogger.warn(`Could not load logo: ${error}`);
    }

    // Get principal name if available
    let principalName = '';
    if (acquiringEntity.principalRawData && acquiringEntity.principalRawData.length > 0) {
      const principal = acquiringEntity.principalRawData[0];
      if (typeof principal === 'object') {
        if ('firstName' in principal && 'lastName' in principal && principal.firstName && principal.lastName) {
          principalName = `${principal.firstName} ${principal.lastName}`;
        } else if ('firstName' in principal && typeof principal.firstName === 'string') {
          principalName = principal.firstName;
        }
      }
    }

    const templateData = {
      name: acquiringEntity.name || '',
      registrationNumber: acquiringEntity.registrationNumber || '',
      incorporationDate: acquiringEntity.incorporationDate || '',
      email: acquiringEntity.email || '',
      phone: acquiringEntity.phone || [],
      country: acquiringEntity.country || '',
      state: acquiringEntity.state || '',
      city: acquiringEntity.city || '',
      zip: acquiringEntity.zip || '',
      address1: acquiringEntity.address1 || '',
      address2: acquiringEntity.address2 || '',
      principalAddress1: acquiringEntity?.principalRawData?.[0].address1 || '',
      principalAddress2: acquiringEntity?.principalRawData?.[0].address2 || '',
      principalCity: acquiringEntity?.principalRawData?.[0].city || '',
      principalState: acquiringEntity?.principalRawData?.[0].state || '',
      principalZip: acquiringEntity?.principalRawData?.[0].zip || '',
      principalCountry: acquiringEntity?.principalRawData?.[0].country || '',
      // Process websites to ensure all nested properties exist
      websites: (acquiringEntity.websites || []).map((website) => ({
        url: website.url || '',
        mccClassification: website.mccClassification || { mcc: '' },
        statementDescriptor: website.statementDescriptor || '',
        cityField: website.cityField || ''
      })),
      submissionId: acquiringEntity.submissionId || '',
      // Add currency data
      currencies: acquiringEntity.additionalData?.currencies || [],
      settlementCurrencies: acquiringEntity.additionalData?.settlementCurrencies || [],
      // Add additionalData object to access nested properties like salesMetrics
      additionalData: acquiringEntity.additionalData || {},
      industryBusinessCategory: (() => {
        const businessOffering = acquiringEntity.additionalData?.businessOffering;
        const categories: string[] = [];
        if (businessOffering && typeof businessOffering === 'object') {
          const offerings = Array.isArray(businessOffering) ? businessOffering : [businessOffering];

          offerings.forEach((offering) => {
            if (offering && offering.industryBusinessCategory) {
              const industryCategories = offering.industryBusinessCategory;

              // Extract category from each industry business category
              if (Array.isArray(industryCategories)) {
                industryCategories.forEach((industry) => {
                  if (industry && industry.category) {
                    categories.push(industry.category);
                  }
                });
              }
            }
          });
        }

        return categories;
      })(),
      targetMarketsAndDistribution: acquiringEntity.additionalData?.targetMarketsAndDistribution || [],
      // Convert cryptoSettlement object to array and ensure all properties exist
      cryptoSettlement: (() => {
        const cryptoData = acquiringEntity.additionalData?.cryptoSettlement;
        if (!cryptoData) return [];

        // Convert to array if it's an object with numeric keys
        const cryptoArray = Array.isArray(cryptoData) ? cryptoData : Object.values(cryptoData);

        // Ensure all items have the required properties
        return cryptoArray.map((item) => {
          // Type assertion to avoid TypeScript errors
          const cryptoItem = item as Record<string, any>;
          return {
            currency: cryptoItem.currency || '',
            cryptocurrency: cryptoItem.cryptocurrency || '',
            percentage: cryptoItem.percentage || '0',
            walletAddress: cryptoItem.walletAddress || '',
            preferredNetwork: cryptoItem.preferredNetwork || ''
          };
        });
      })(),
      bankSettlement: acquiringEntity.additionalData?.bankSettlement || [],
      // Add formatted date
      date: date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      // Add logo and other computed values
      logoSrc: `data:image/jpeg;base64,${logoBase64}`,
      principalName
    };

    // Generate HTML using the template
    return template(templateData);
  } catch (error: any) {
    throw error;
  }
}

/**
 * Generate a PDF from HTML content using Puppeteer
 * @param html HTML content
 * @param acquiringEntity The acquiring entity data for filename generation
 * @returns Object containing Base64 encoded PDF content and email
 */
export async function generatePDFFromHTML(
  html: string,
  acquiringEntity: AcquiringEntity
): Promise<PDFGenerationResult> {
  try {
    // Launch a headless browser
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-gpu', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Set viewport to ensure proper rendering
    await page.setViewport({
      width: 1200,
      height: 1600,
      deviceScaleFactor: 2
    });

    // Set content and wait for network resources to load
    await page.setContent(html, {
      waitUntil: ['domcontentloaded', 'networkidle0'],
      timeout: 5000
    });

    // Ensure content is fully loaded
    await page.evaluate(() => {
      return new Promise((resolve) => {
        if (document.readyState === 'complete') {
          resolve(true);
        } else {
          window.addEventListener('load', () => resolve(true));
        }
      });
    });

    // Generate PDF with improved settings
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      preferCSSPageSize: true,
      margin: {
        top: '0px',
        right: '20px',
        bottom: '20px',
        left: '20px'
      },
      scale: 0.98, // Slightly scale down to avoid text overflow
      displayHeaderFooter: false, // Disable default headers/footers
      omitBackground: false, // Keep background colors
      timeout: 60000 // Longer timeout for complex documents
    });

    await browser.close();

    // Convert buffer to Base64
    const base64Content = Buffer.from(pdfBuffer).toString('base64');

    const email = acquiringEntity.correspondenceEmail || '';

    // Generate a filename based on the entity details
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `merchant_agreement_${acquiringEntity.registrationNumber}_${timestamp}.pdf`;

    acquiringEntityLogger.info(
      `✅ PDF generated successfully for acquiring entity with registration number: ${acquiringEntity.registrationNumber}`
    );

    return {
      base64Content,
      email,
      filename
    };
  } catch (error: any) {
    throw error;
  }
}

/**
 * Generate a PDF document for an Acquiring Entity and return it as Base64 encoded string
 * @param acquiringEntity The acquiring entity data
 * @returns Object containing Base64 encoded PDF content and email
 */
export async function generateAcquiringEntityPDF(acquiringEntity: AcquiringEntity): Promise<PDFGenerationResult> {
  try {
    // Generate HTML from template
    const html = generateMerchantAgreementHTML(acquiringEntity);

    // Generate PDF from HTML
    return await generatePDFFromHTML(html, acquiringEntity);
  } catch (error: any) {
    throw error;
  }
}
