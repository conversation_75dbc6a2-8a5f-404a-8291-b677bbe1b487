import { kafkaService } from '../../submodules/ryvyl-commons/services/kafkaService';
import logger from '../../submodules/ryvyl-commons/services/loggerService';

export async function connectToKafka() {
  try {
    await kafkaService.connect();
  } catch (error: any) {
    throw new Error(`❌ Error connecting to Kafka:${error.message}`);
  }
}

export async function disconnectFromKafka() {
  try {
    await kafkaService.disconnect();
  } catch (error: any) {
    logger.error(`❌ Error disconnecting from Kafka:${error.message}`);
  }
}
