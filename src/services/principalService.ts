import { CreateApplicantRequest, WebhookApplicant } from '../interfaces/sumsubApplicantInterface';
import { Principal } from '../interfaces/principalInterface';
import PrincipalModel, { PrincipalModelDocument } from '../models/mongo/principal';
import { ObjectId } from 'mongoose';
import { createApplicant, generateExternalWebSDKLink, rumAMLApplicantCheck } from './sumsubService';
import { SUMSUB } from '../config/config';
import safeJsonParse from '../utils/jsonParse';
import { acquiringEntityLogger, principalLogger } from '../utils/logger';
import { getAcquiringEntityById } from './acquiringEntityService';
import HttpException from '@submodules/ryvyl-commons/classes/HttpException';
import { ReviewAnswer, ReviewStatus } from '../enums/sumsub';

export const createPrincipal = async (data: Principal) => {
  const principal = await PrincipalModel.create(data);
  return principal;
};

export const getPrincipalById = async (id: string) => {
  const principal = await PrincipalModel.findById(id);
  return principal;
};

export async function deletePrincipal(id: string) {
  await PrincipalModel.findByIdAndDelete(id);
}

export async function updatePrincipal(id: string, data: Principal) {
  const principal = await PrincipalModel.findByIdAndUpdate(id, data, { new: true });
  return principal;
}

export const getPrincipalDefaultValues = () => {
  const defaultPrincipal: Partial<Principal> = {
    firstName: '',
    middleName: '',
    lastName: '',
    email: '',
    phone: '',
    country: '',
    state: '',
    city: '',
    zip: '',
    address1: '',
    address2: '',
    dateOfBirth: '',
    passportNumber: '',
    driverLicenseNumber: ''
  };
  return defaultPrincipal;
};

/**
 * Create a Sumsub applicant for a principal
 */
export async function createSumsubApplicantForPrincipal(principalId: string): Promise<boolean> {
  const principal = await getPrincipalById(principalId);
  if (!principal) {
    principalLogger.error(`Principal with this id: ${principalId} not found`);
    return false;
  }
  if (!principal.email) {
    principalLogger.error(`Principal with this id: ${principalId} does not have email`);
    return false;
  }

  if (principal.sumsub?.sumsubId) {
    return false;
  }

  const sumsubData: CreateApplicantRequest = {
    externalUserId: (principal._id as ObjectId).toString(),
    type: 'individual',
    fixedInfo: {
      firstName: principal.firstName,
      lastName: principal.lastName
    }
  };

  const levelName = SUMSUB.ACQUIRING_ENTITY_BENEFICIARY_LEVEL_NAME;

  try {
    const sumsubApplicant = await createApplicant(levelName, sumsubData);
    let sumsubId: string | null = null;
    if (sumsubApplicant?.data?.id) {
      sumsubId = sumsubApplicant.data.id as string;
      if (principal.sumsub) {
        principal.sumsub.sumsubId = sumsubId;
        principal.sumsub.createApplicantError = undefined;
      } else {
        principal.sumsub = {
          sumsubId: sumsubId,
          createApplicantError: undefined
        };
      }
      await principal.save();
      principalLogger.info(`Principal with this id: ${principalId} created in Sumsub successfully`);
      return true;
    }
    principalLogger.error(`Error creating principal in Sumsub, sumsubId is not found`);
    return false;
  } catch (error: any) {
    if (principal) {
      const message = safeJsonParse(error.message);
      if (principal.sumsub) {
        principal.sumsub.createApplicantError = message;
      } else {
        principal.sumsub = {
          createApplicantError: message
        };
      }

      principal.save().catch((err: any) => {
        principalLogger.error(
          `Failed to save principal with Sumsub error:${error.message}, saved error: ${err.message}`
        );
      });
    }
    principalLogger.error(`Error creating principal in Sumsub: ${error.message}`);
    return false;
  }
}

/**
 * Get the Sumsub applicant web SDK link for a principal to start the KYC process
 */
export async function getSumsubApplicantWebSDKLinkForPrincipal(principalId: string) {
  const principal = await getPrincipalById(principalId);
  if (!principal) {
    principalLogger.error(`Principal with this id: ${principalId} not found`);
    return;
  }
  const sumsubLevelName = SUMSUB.ACQUIRING_ENTITY_BENEFICIARY_LEVEL_NAME;

  const sumsubData = {
    levelName: sumsubLevelName,
    userId: (principal._id as ObjectId).toString(),
    applicantIdentifiers: {
      email: principal.email
    }
  };
  const sumsubApplicant = await generateExternalWebSDKLink(sumsubData);
  return sumsubApplicant?.data?.url;
}

/**
 * Handle the Sumsub applicant webhook data for a principal after has been checked in Sumsub
 * Check if all principals have passed the Sumsub check for an acquiring entity
 * Run the AML check for an acquiring entity again if all principals have passed the Sumsub check
 */
export async function handleSumsubApplicantAcquiringEntityPrincipalKycCheckDataWebhook(
  topic: string,
  data: WebhookApplicant
) {
  const principalMongoId = data?.externalUserId;

  let principal: PrincipalModelDocument | null = null;
  try {
    if (!principalMongoId) {
      throw new HttpException(400, `Webhook data does not provide externalUserId: ${principalMongoId}.`);
    }
    try {
      principal = await getPrincipalById(principalMongoId);
    } catch (error) {
      throw new HttpException(
        400,
        `Something went wrong when we try to find principal with externalUserId: ${principalMongoId}.`
      );
    }

    if (!principal) {
      throw new HttpException(400, `We do not have record with this principal mongo id: ${principalMongoId}.`);
    }

    // Here we assert that user is neither null nor undefined
    if (
      data?.reviewResult?.reviewAnswer == ReviewAnswer.Red ||
      data?.reviewResult?.reviewAnswer == ReviewAnswer.Green
    ) {
      if (principal.sumsub) {
        principal.sumsub.reviewStatus = data.reviewStatus as ReviewStatus;
        principal.sumsub.reviewAnswer = data.reviewResult.reviewAnswer as ReviewAnswer;
        principal.sumsub.AMLError = data.reviewResult?.moderationComment
          ? data.reviewResult.moderationComment
          : undefined;
      } else {
        principal.sumsub = {
          reviewStatus: data.reviewStatus as ReviewStatus,
          reviewAnswer: data.reviewResult.reviewAnswer as ReviewAnswer,
          AMLError: data.reviewResult?.moderationComment ? data.reviewResult.moderationComment : undefined
        };
      }

      try {
        await principal.save();

        const isAllPrincipalPassSumsub = await checkIfAllPrincipalsPassSumsubCheckForAcquiringEntity(
          (principal._id as ObjectId).toString()
        );

        if (principal.acquiringEntityId) {
          // Run the AML check for an acquiring entity again if all principals have passed the Sumsub check
          if (isAllPrincipalPassSumsub) {
            const acquiringEntity = await getAcquiringEntityById((principal.acquiringEntityId as ObjectId).toString());

            if (!acquiringEntity?.sumsub?.sumsubId) {
              acquiringEntityLogger.error(
                `We cannot run again rumAMLApplicantCheck for acquiringEntity, because we cannot find a entity or her sumsubId.`
              );
            } else {
              await rumAMLApplicantCheck(acquiringEntity?.sumsub?.sumsubId as string);
            }
          }
        } else {
          principalLogger.error(
            `Principal with this id: ${principal._id} does not have acquiringEntityId to run again the AML chaeck.`
          );
        }
      } catch (error: any) {
        const message = `Failed to saved a principal with mongoId: ${data.externalUserId} error: ${error.message}.`;
        principalLogger.error(message);
        throw new Error(message);
      }
    } else {
      throw new HttpException(400, `Unknown reviewAnswer: ${data.reviewResult.reviewAnswer}.`);
    }
  } catch (error: any) {
    principalLogger.error(`${error.message}`);
  }
}

/**
 * Check if all principals have passed the Sumsub check for an acquiring entity
 */
async function checkIfAllPrincipalsPassSumsubCheckForAcquiringEntity(principalId: string) {
  try {
    const principal = await getPrincipalById(principalId);
    if (!principal) {
      throw new Error(`Principal with this id: ${principalId} not found`);
    }
    const acquiringEntity = await getAcquiringEntityById((principal.acquiringEntityId as ObjectId).toString());
    if (!acquiringEntity) {
      throw new Error(`Acquiring entity with this id: ${principal.acquiringEntityId} not found`);
    }

    const isAllPrincipalCreated = acquiringEntity?.principalsIds?.length === acquiringEntity.principalRawData?.length;
    if (
      !acquiringEntity?.principalsIds ||
      acquiringEntity?.principalsIds?.length <= 0 ||
      !acquiringEntity.principalRawData ||
      acquiringEntity.principalRawData.length <= 0 ||
      !isAllPrincipalCreated
    ) {
      throw new Error(`All principals are not created for acquiring entity with id: ${acquiringEntity._id}`);
    }
    let allPrincipalPassSumsub = 0;
    for (const principalEl of acquiringEntity.principalsIds) {
      const principal: PrincipalModelDocument = principalEl as unknown as PrincipalModelDocument;
      if (
        principal.sumsub?.reviewStatus === ReviewStatus.Completed &&
        principal.sumsub?.reviewAnswer === ReviewAnswer.Green
      ) {
        allPrincipalPassSumsub += 1;
      }
    }
    if (allPrincipalPassSumsub === acquiringEntity.principalsIds.length) {
      return true;
    }
    return false;
  } catch (error: any) {
    principalLogger.error(`checkIfAllPrincipalsPassSumsubCheckForAcquiringEntity error: ${error.message}`);
    return false;
  }
}
