import { checkAcquiringEntityRequest } from '../requests/ryvylVisaVmssRequests';
import {
  Principal,
  ResponseTerminatedMatchRyvylVmss,
  RootRequestVmssMerchantMatch,
  VisaMatchData,
  VisaMatchDataItem
} from '../interfaces/visaVmssInterface';
import { getAcquiringEntityById } from './acquiringEntityService';
import { AcquiringEntityModelDocument } from '../models/mongo/acquiringEntity';
import { CustomError } from '../classes/CustomError';
import { getTwoLetterCodeFromFullName } from '../utils/countryFormat';
import { BusinessCategoryVmss } from '../enums/businessCategory';
import { visaVmssLogger } from '../utils/logger';
import { Types } from 'mongoose';
import { getPaginationData } from '../utils/pagePagination';
import { flattenObject, getUniqueKeys, sortKeys, transformSpecialKeys } from '../utils/objectTranformation';

/*
 * This is when we want to check the acquiring entity match in Visa Vmss
 * with the urls to 3 for an acquiring entity.
 * Because Visa has a limit of 3 urls to check for an acquiring entity.
 */
// export async function vmssAcquiringEntityMatch(
//   acquiringEntity: AcquiringEntityModelDocument
// ): Promise<ResponseTerminatedMatchRyvylVmss> {
//   const visaVmssRequiredFields = [
//     'city',
//     'globalSearch',
//     'address1',
//     'country',
//     'phone',
//     'country',
//     'name',
//     'principalsIds.0.firstName'
//   ];

//   const visaVmssRequiredFieldsByRyvyl = [
//     'globalSearch',
//     'businessCategoryVmss',

//     'name',
//     'address1',
//     'city',
//     'country',
//     'zip',
//     'phone',
//     'registrationNumber',
//     'tradeOverInternet',
//     'principalsIds.0.firstName',
//     'principalsIds.0.lastName'
//     // 'principalsIds.0.country',
//     // 'principalsIds.0.phone1'
//   ];

//   if (acquiringEntity.tradeOverInternet === true) {
//     if (!acquiringEntity.websites || acquiringEntity.websites.length <= 0) {
//       visaVmssRequiredFieldsByRyvyl.push('websites');
//     }
//   }

//   // Function to check if an object has the required properties
//   function hasProperty(obj: any, path: string): boolean {
//     return (
//       path.split('.').reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined), obj) !== undefined
//     );
//   }

//   function validateFields(entity: any, requiredFields: string[]): string[] {
//     return requiredFields.filter((field) => {
//       // Missing this fields because we need to add them to the acquiring entity manually
//       const missTheFields = ['globalSearch'];
//       if (missTheFields.includes(field)) {
//         return false;
//       }

//       return !hasProperty(entity, field);
//     });
//   }

//   const missingVisaFields = validateFields(acquiringEntity, visaVmssRequiredFields);
//   const missingVisaRyvylFields = validateFields(acquiringEntity, visaVmssRequiredFieldsByRyvyl);

//   const principals: Principal[] =
//     acquiringEntity.principalsIds?.map((principal: any, index: number) => {
//       if (!principal.firstName) {
//         missingVisaRyvylFields.push(`Missing required fields on ${index} principal field: firstName`);
//       }

//       if (!principal.lastName) {
//         missingVisaRyvylFields.push(`Missing required fields on ${index} principal field: lastName`);
//       }
//       if (!principal.phone) {
//         missingVisaRyvylFields.push(`Missing required fields on ${index} principal field: phone`);
//       }

//       let name = principal.firstName;
//       if (principal.middleName) {
//         name += ` ${principal.middleName}`;
//       }
//       if (principal.lastName) {
//         name += ` ${principal.lastName}`;
//       }

//       return {
//         name: name,
//         businessPhoneNumber: principal.phone,
//         ...(principal.email && { businessEmailAddress: principal.email }),
//         ...(principal.driverLicenseNumber && { driverLicenseNumber: principal.driverLicenseNumber }),
//         ...(principal.passportNumber && { passportNumber: principal.passportNumber }),
//         ...(principal.principalId && { principalID: principal.principalId }),
//         ...(principal.residentIDOrNationalID && { residentIDOrNationalID: principal.residentIDOrNationalID })
//       };
//     }) || [];

//   if (missingVisaFields.length > 0 || missingVisaRyvylFields.length > 0) {
//     throw new CustomError(
//       `Missing required fields: ${[...new Set([...missingVisaFields, ...missingVisaRyvylFields])].join(', ')}`,
//       400
//     );
//   }

//   const countryRegion = getTwoLetterCodeFromFullName(acquiringEntity.country as string);
//   if (!countryRegion) {
//     throw new CustomError('Country region not found', 400);
//   }

//   if (principals.length <= 0) {
//     throw new CustomError('Principals not found!', 400);
//   }

//   const visaVmssData: RootRequestVmssMerchantMatch = {
//     searchTerminatedRequest: {
//       acquirerCountryOrRegion: countryRegion,
//       globalSearch: false,
//       terminatedRecordSearchCriteria: {
//         legalName: acquiringEntity.name,
//         businessEmailAddress: acquiringEntity.email,
//         businessRegistrationNumber: acquiringEntity.registrationNumber,
//         tradeOverInternet: acquiringEntity.tradeOverInternet as boolean,
//         businessPhoneNumbers: acquiringEntity.phone as string[],
//         category: (acquiringEntity.businessCategoryVmss as BusinessCategoryVmss) ?? BusinessCategoryVmss.MERCHANT,
//         DBAName: acquiringEntity.name,
//         // To remove
//         // merchantCategoryCodes: ['0742'],
//         webAddresses: acquiringEntity.websites as string[],
//         address: {
//           streetAddress: acquiringEntity.address1 as string,
//           city: acquiringEntity.city as string,
//           countryOrRegion: countryRegion,
//           ...(acquiringEntity.state && { stateOrProvince: acquiringEntity.state }),
//           zipOrPostalCode: acquiringEntity.zip as string
//         },
//         principals: principals
//       }
//     }
//   };
//   try {
//     const response = await checkAcquiringEntityRequest(visaVmssData);

//     return response;
//   } catch (error: any) {
//     if (error?.response?.data?.message) {
//       throw new CustomError(error?.response?.data?.message, error?.response?.status ?? 400);
//     }
//     throw new Error(`Error in checkAcquiringEntity Visa Vmss: ${error.message}`);
//   }
// }

/*
 * This is when we want to check the acquiring entity match in Visa Vmss
 * with the urls are more than 3 for an acquiring entity.
 * We are making several request to match all the urls.
 */
export async function vmssAcquiringEntityMatchWithDescriptionInformation(
  acquiringEntity: AcquiringEntityModelDocument
): Promise<
  {
    url: string | undefined;
    error?: any;
    response?: ResponseTerminatedMatchRyvylVmss;
  }[]
> {
  const visaVmssRequiredFields = [
    'city',
    'globalSearch',
    'address1',
    'country',
    'phone',
    'country',
    'name',
    'principalsIds.0.firstName'
  ];

  const visaVmssRequiredFieldsByRyvyl = [
    'globalSearch',
    'businessCategoryVmss',

    'name',
    'address1',
    'city',
    'country',
    'zip',
    'phone',
    'registrationNumber',
    'tradeOverInternet',
    'principalsIds.0.firstName',
    'principalsIds.0.lastName'
    // 'principalsIds.0.country',
    // 'principalsIds.0.phone1'
  ];

  let addWebAddresses = false;
  if (acquiringEntity.tradeOverInternet === true) {
    if (!acquiringEntity.websites || acquiringEntity.websites.length <= 0) {
      visaVmssRequiredFieldsByRyvyl.push('websites');
      addWebAddresses = true;
    }
  }

  // Function to check if an object has the required properties
  function hasProperty(obj: any, path: string): boolean {
    return (
      path.split('.').reduce((acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined), obj) !== undefined
    );
  }

  function validateFields(entity: any, requiredFields: string[]): string[] {
    return requiredFields.filter((field) => {
      // Missing this fields because we need to add them to the acquiring entity manually
      const missTheFields = ['globalSearch'];
      if (missTheFields.includes(field)) {
        return false;
      }

      return !hasProperty(entity, field);
    });
  }

  const missingVisaFields = validateFields(acquiringEntity, visaVmssRequiredFields);
  const missingVisaRyvylFields = validateFields(acquiringEntity, visaVmssRequiredFieldsByRyvyl);

  const principals: Principal[] =
    acquiringEntity.principalsIds?.map((principal: any, index: number) => {
      if (!principal.firstName) {
        missingVisaRyvylFields.push(`Missing required fields on ${index} principal field: firstName`);
      }

      if (!principal.lastName) {
        missingVisaRyvylFields.push(`Missing required fields on ${index} principal field: lastName`);
      }
      if (!principal.phone) {
        missingVisaRyvylFields.push(`Missing required fields on ${index} principal field: phone`);
      }

      let name = principal.firstName;
      if (principal.middleName) {
        name += ` ${principal.middleName}`;
      }
      if (principal.lastName) {
        name += ` ${principal.lastName}`;
      }

      return {
        name: name,
        businessPhoneNumber: principal.phone,
        ...(principal.email && { businessEmailAddress: principal.email }),
        ...(principal.driverLicenseNumber && { driverLicenseNumber: principal.driverLicenseNumber }),
        ...(principal.passportNumber && { passportNumber: principal.passportNumber }),
        ...(principal.principalId && { principalID: principal.principalId }),
        ...(principal.residentIDOrNationalID && { residentIDOrNationalID: principal.residentIDOrNationalID })
      };
    }) || [];

  if (missingVisaFields.length > 0 || missingVisaRyvylFields.length > 0) {
    throw new CustomError(
      `Missing required fields: ${[...new Set([...missingVisaFields, ...missingVisaRyvylFields])].join(', ')}`,
      400
    );
  }

  const countryRegion = getTwoLetterCodeFromFullName(acquiringEntity.country as string);
  if (!countryRegion) {
    throw new CustomError('Country region not found', 400);
  }

  if (principals.length <= 0) {
    throw new CustomError('Principals not found!', 400);
  }

  if (!acquiringEntity.websites || acquiringEntity.websites.length <= 0) {
    throw new CustomError('Websites not found!', 400);
  }

  if (acquiringEntity.isAllWebsitesClassified == false) {
    throw new CustomError('Not all websites are classified!', 400);
  }

  let visaVmssData: RootRequestVmssMerchantMatch[] = [];

  for (let i = 0; i < acquiringEntity.websites.length; i++) {
    // const chunk = acquiringEntity.websites.slice(i, i + 3);
    const websiteData = acquiringEntity.websites[i];
    const url = websiteData.url;
    const mcc = websiteData.mccClassification?.mcc;
    const tradingName = acquiringEntity.tradingName ?? acquiringEntity.name;
    visaVmssData.push(
      formVisaVmssData(acquiringEntity, tradingName, countryRegion, principals, [url], [mcc] as string[])
    );
  }

  const visaMatchDataArrayResponse: {
    url: string | undefined;
    error?: any;
    response?: ResponseTerminatedMatchRyvylVmss;
  }[] = [];

  for (let i = 0; i < visaVmssData.length; i++) {
    const requestData = visaVmssData[i];
    try {
      const response = await checkAcquiringEntityRequest(requestData);
      // responses.push(response);
      visaMatchDataArrayResponse.push({
        url: requestData?.searchTerminatedRequest?.terminatedRecordSearchCriteria?.webAddresses?.[0],
        response: response
      });
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message ?? error.message;
      visaMatchDataArrayResponse.push({
        url: requestData?.searchTerminatedRequest?.terminatedRecordSearchCriteria?.webAddresses?.[0],
        error: errorMessage
      });
    }
  }

  return visaMatchDataArrayResponse;
}

function formVisaVmssData(
  acquiringEntity: AcquiringEntityModelDocument,
  tradingName: string,
  countryRegion: string,
  principals: Principal[],
  website: string[],
  mcc: string[]
) {
  const visaVmssData = {
    searchTerminatedRequest: {
      acquirerCountryOrRegion: countryRegion,
      globalSearch: false,
      terminatedRecordSearchCriteria: {
        legalName: acquiringEntity.name,
        businessEmailAddress: acquiringEntity.email,
        businessRegistrationNumber: acquiringEntity.registrationNumber,
        tradeOverInternet: acquiringEntity.tradeOverInternet as boolean,
        businessPhoneNumbers: acquiringEntity.phone as string[],
        category: (acquiringEntity.businessCategoryVmss as BusinessCategoryVmss) ?? BusinessCategoryVmss.MERCHANT,
        DBAName: tradingName,
        // To remove
        // merchantCategoryCodes: ['0742'],
        merchantCategoryCodes: mcc,
        webAddresses: website,
        address: {
          streetAddress: acquiringEntity.address1 as string,
          city: acquiringEntity.city as string,
          countryOrRegion: countryRegion,
          ...(acquiringEntity.state && { stateOrProvince: acquiringEntity.state }),
          zipOrPostalCode: acquiringEntity.zip as string
        },
        principals: principals
      }
    }
  };

  return visaVmssData;
}

function mapAcquiringEntityToVisaResponse(acquiringEntity: AcquiringEntityModelDocument) {
  let principals: any[] = [];
  if (acquiringEntity.principalsIds) {
    // @ts-ignore
    principals = acquiringEntity.principalsIds.map((principal: PrincipalModelDocument) => {
      let name = '';
      if (principal.firstName) {
        name += principal.firstName;
      }
      if (principal.middleName) {
        name += ` ${principal.middleName}`;
      }
      if (principal.lastName) {
        name += ` ${principal.lastName}`;
      }
      return {
        name: name,
        businessEmailAddress: principal.email,
        businessPhoneNumber: principal.phone,
        ...(principal.driverLicenseNumber && { driverLicenseNumber: principal.driverLicenseNumber }),
        ...(principal.passportNumber && { passportNumber: principal.passportNumber })
      };
    });
  }

  let webAddresses: any[] = [];
  if (acquiringEntity.websites) {
    webAddresses = acquiringEntity.websites.map((website: any) => {
      return {
        url: website.url,
        mcc: website.mccClassification?.mcc
      };
    });
  }

  const data = {
    _id: (acquiringEntity._id as Types.ObjectId).toString(),
    ...(acquiringEntity.country && { acquirerCountryOrRegion: getTwoLetterCodeFromFullName(acquiringEntity.country) }),
    terminatedRecord: {
      address: {
        city: acquiringEntity.city,
        ...(acquiringEntity.country && { countryOrRegion: getTwoLetterCodeFromFullName(acquiringEntity.country) }),
        streetAddress: acquiringEntity.address1,
        ...(acquiringEntity.state && { stateOrProvince: acquiringEntity.state }),
        ...(acquiringEntity.zip && { zipOrPostalCode: acquiringEntity.zip })
      },
      businessPhoneNumbers: acquiringEntity.phone,
      category: acquiringEntity.businessCategoryVmss,
      DBAName: acquiringEntity.name,

      ...(acquiringEntity.tradeOverInternet && { tradeOverInternet: acquiringEntity.tradeOverInternet }),
      ...(acquiringEntity.email && { businessEmailAddress: acquiringEntity.email }),
      businessRegistrationNumber: acquiringEntity.registrationNumber,
      legalName: acquiringEntity.name,
      ...(acquiringEntity.taxId && { taxID: acquiringEntity.taxId }),
      ...(webAddresses.length > 0 && { webAddresses: webAddresses }),
      principals: principals
    }
  };
  return data;
}

export async function getAcquiringEntityMatchInVisaVmssDashboard(
  acquiringEntityId: string,
  page_length: number,
  page_offset: number
) {
  try {
    const acquiringEntity: AcquiringEntityModelDocument | null = await getAcquiringEntityById(acquiringEntityId);

    if (!acquiringEntity) {
      throw new CustomError(`Acquiring entity with id: ${acquiringEntityId} not found`, 404);
    }
    if (!acquiringEntity.visaMatchData || acquiringEntity.visaMatchData.length <= 0) {
      throw new CustomError(`This entity does not pass Visa Vmss screening!`, 400);
    }

    const visaMatchData: VisaMatchData = acquiringEntity.visaMatchData[acquiringEntity.visaMatchData.length - 1];

    if (visaMatchData.errorMessageRequest) {
      throw new CustomError(
        `Failed to make Visa Vmss check because of error: ${visaMatchData.errorMessageRequest}`,
        400
      );
    }

    const allChecks = visaMatchData.data;

    const visaMatchDataItems: VisaMatchDataItem[] = visaMatchData.data ?? [];

    const matchDataArray: ResponseTerminatedMatchRyvylVmss[] = [];
    visaMatchDataItems.forEach((el: VisaMatchDataItem) => {
      if (el.isMatch && el?.visaResultData?.possibleMatches?.matchedRecords?.length > 0) {
        matchDataArray.push(...el.visaResultData.possibleMatches.matchedRecords);
      }
    });

    const mappedAcquiringEntity = mapAcquiringEntityToVisaResponse(acquiringEntity);

    if (!matchDataArray) {
      throw new CustomError(`Visa Vmss Matches data in not provided!`, 400);
    }

    // const totalDocs = acquiringEntity.visaTotalCountMatches ?? 0; // need to be change
    const totalDocs = matchDataArray.length;
    const paginationData = getPaginationData(totalDocs, page_length, page_offset + 1);

    //@ts-ignore
    matchDataArray.unshift({ ...mappedAcquiringEntity });
    const dataKeyValuePairs = matchDataArray.map((match: any) => {
      return {
        _id: match?._id,
        merchant: flattenObject(match?.terminatedRecord, '', ['principals', 'terminatedRecordMatch', 'webAddresses']),
        acquirerBID: match?.acquirerBID,
        merchantMatch: match?.terminatedRecordMatch
      };
    });

    const transformedDataKeyValuePairs = dataKeyValuePairs.map((el: any) => {
      let item = el.merchant;
      for (let key in item) {
        if (item.hasOwnProperty(key)) {
          if (key === 'principals') {
            const result = transformSpecialKeys(key, item[key]);
            delete item[key];
            item = { ...item, ...result };
            continue;
          }
        }
      }
      return { ...item, merchantMatch: el.merchantMatch, acquirerBID: el.acquirerBID, _id: el._id };
    });
    const arrayOfColumns = getUniqueKeys(transformedDataKeyValuePairs, ['terminatedRecordMatch']);

    const sortedColumns = sortKeys(arrayOfColumns as string[], [
      'DBAName',
      'businessRegistrationNumber',
      'acquirerBID'
    ]);

    return {
      ...paginationData,
      docs: transformedDataKeyValuePairs,
      columnsData: sortedColumns,
      totalRequests: visaMatchData.requestMadeToCheckMerchant,
      failedRequests: visaMatchData.failedRequest,
      createdAt: visaMatchData.createdAt,
      _id: visaMatchData._id
    };
  } catch (error: any) {
    visaVmssLogger.error(
      `Failed to check acquiring entity with id: ${acquiringEntityId} match in Visa Vmss: ${error.message}`
    );
    throw error;
  }
}
