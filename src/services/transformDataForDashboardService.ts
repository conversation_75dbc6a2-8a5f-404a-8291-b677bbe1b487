import { MastercardMatchData } from '../interfaces/mastercardMatchProInterfaces';
import { VisaMatchData } from '../interfaces/visaVmssInterface';
import { AcquiringEntityModelDocument } from '../models/mongo/acquiringEntity';

export async function transformDataForGetAllAcquiringEntities(acquiringEntities: AcquiringEntityModelDocument[]) {
  if (!acquiringEntities.length) return [];
  const acquiringEntityData = acquiringEntities.map((acquiringEntityDocument: AcquiringEntityModelDocument) => {
    const acquiringEntity = acquiringEntityDocument.toObject();
    const lastVisaMatchData: VisaMatchData | undefined = acquiringEntity?.visaMatchData?.length
      ? acquiringEntity.visaMatchData[acquiringEntity.visaMatchData.length - 1]
      : undefined;
    const lastMastercardMatchData: MastercardMatchData | undefined = acquiringEntity?.mastercardMatchData?.length
      ? acquiringEntity.mastercardMatchData[acquiringEntity.mastercardMatchData.length - 1]
      : undefined;

    delete lastVisaMatchData?.data;
    delete lastMastercardMatchData?.data;

    return {
      ...acquiringEntity,
      visaMatchData: lastVisaMatchData,
      mastercardMatchData: lastMastercardMatchData
    };
  });
  return acquiringEntityData;
}
