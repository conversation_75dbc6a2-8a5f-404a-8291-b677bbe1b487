import Joi from 'joi';

import { AcquiringEntity, AdditionalData } from '../interfaces/acquiringEntityInterface';
import { Principal } from '../interfaces/principalInterface';
import { BusinessCategoryVmss } from '../enums/businessCategory';
import {
  ManualApproveAcquire,
  ManualApproveAcquireByRefNumber
} from '../interfaces/acquiringEntityBodyRequestInterface';
import { AcquiringEntityEnum } from '../enums/acquringEntityEnum';

const principalSchema = Joi.object<Principal>({
  firstName: Joi.string().required(),
  middleName: Joi.string().optional(),
  lastName: Joi.string().required(),
  email: Joi.string().email().optional(),
  phone: Joi.string().required(),
  country: Joi.string().optional(),
  state: Joi.string().optional(),
  city: Joi.string().optional(),
  address1: Joi.string().optional(),
  address2: Joi.string().optional(),
  zip: Joi.string().optional(),
  dateOfBirth: Joi.string().isoDate().optional(),
  passportNumber: Joi.string().optional(),
  driverLicenseNumber: Joi.string().optional(),
  positionInCompany: Joi.string().optional()
});

export const baseAcquiringEntitySchema = Joi.object<AcquiringEntity>({
  name: Joi.string().required(),
  entityType: Joi.string()
    .required()
    .valid(...Object.values(AcquiringEntityEnum)),
  pspIsoReferralAgentName: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  registrationNumber: Joi.string().required(),
  incorporationDate: Joi.string().optional(),
  email: Joi.string().email().optional(),
  phone: Joi.string().required(),
  country: Joi.string().required(),
  state: Joi.string().optional(),
  city: Joi.string().required(),
  zip: Joi.string().required(),
  address1: Joi.string().required(),
  address2: Joi.string().optional(),
  tradeOverInternet: Joi.boolean().optional(),
  websites: Joi.array()
    .items(
      Joi.object({
        url: Joi.string().uri().required(),
        statementDescriptor: Joi.string().optional(),
        cityField: Joi.string().optional()
      })
    )
    .optional(),
  principalRawData: Joi.array().items(principalSchema).optional(),
  businessCategoryVmss: Joi.string()
    .optional()
    .valid(...Object.values(BusinessCategoryVmss)),
  taxId: Joi.string().optional(),
  tradingName: Joi.string().optional(),
  address1TradingAs: Joi.string().optional(),
  address2TradingAs: Joi.string().optional(),
  cityTradingAs: Joi.string().optional(),
  stateTradingAs: Joi.string().optional(),
  zipTradingAs: Joi.string().optional(),
  countryTradingAs: Joi.string().optional(),
  tradeName: Joi.string().optional(),
  submissionId: Joi.string().optional(),
  additionalData: Joi.object<AdditionalData>({
    currencies: Joi.array().items(Joi.string().required()).optional(),
    settlementCurrencies: Joi.array().items(Joi.string().required()).optional(),
    integrationsRequirements: Joi.object({
      integrationType: Joi.string().optional(),
      pciComplianceStatus: Joi.string().optional(),
      thirdPartyGateway: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
      integrationOption: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
      paymentPageHostingPreference: Joi.string().optional(),
      paymentPageURL: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
      mandatory3ds: Joi.boolean().optional()
    }).optional(),
    businessOffering: Joi.object({
      industryBusinessCategory: Joi.array()
        .items(
          Joi.object({
            category: Joi.string().required(),
            subcategory: Joi.array().items(Joi.string().optional()).optional()
          })
        )
        .optional(),
      productOrServicesDescription: Joi.string().optional()
    }).optional(),
    targetMarketsAndDistribution: Joi.array()
      .items(
        Joi.object({
          country: Joi.string().required(),
          volumePercentage: Joi.string().required()
        })
      )
      .optional(),
    cryptoSettlement: Joi.array()
      .items(
        Joi.object({
          currency: Joi.string().required(),
          percentage: Joi.string().required(),
          walletAddress: Joi.string().required(),
          preferredNetwork: Joi.string().required(),
          cryptocurrency: Joi.string().required()
        })
      )
      .optional(),
    bankSettlement: Joi.array()
      .items(
        Joi.object({
          currency: Joi.string().required(),
          bankCountry: Joi.alternatives().try(Joi.string().email(), Joi.allow(null)).optional(),
          bankName: Joi.alternatives().try(Joi.string().email(), Joi.allow(null)).optional(),
          bankIban: Joi.string().required(),
          bankCode: Joi.alternatives().try(Joi.string().email(), Joi.allow(null)).optional(),
          bankAccountHolder: Joi.alternatives().try(Joi.string().email(), Joi.allow(null)).optional()
        })
      )
      .optional(),
    salesMetrics: Joi.object({
      totalMonthlySalesVolume: Joi.string().optional(),
      expectedMonthlyVolume: Joi.string().optional(),
      numberOfExpectedMonthlyTransactions: Joi.string().optional(),
      averageTransactionAmount: Joi.string().optional(),
      highestTransactionAmount: Joi.string().optional(),
      lowestTransactionAmount: Joi.string().optional()
    }).optional(),
    shippingInfo: Joi.object({
      shippingMethods: Joi.string().optional(),
      trackingNumber: Joi.string().optional(),
      shippingFeesAndDeliveryTimes: Joi.string().optional()
    }).optional(),
    rdrSettings: Joi.object({
      rdrEnrollmentPreference: Joi.string().optional(),
      automaticRdrRefunds: Joi.object({
        option: Joi.string().optional(),
        thresholdAmount: Joi.number().optional()
      }).optional()
    }).optional(),
    masterCardSettings: Joi.object({
      masterCardEnrollmentPreference: Joi.string().optional(),
      automaticMasterCardRefunds: Joi.object({
        option: Joi.string().optional(),
        thresholdAmount: Joi.number().optional()
      }).optional()
    }).optional(),
    settlementAccountMetrics: Joi.object({
      monthlySalesVolume: Joi.string().optional(),
      estimatedMonthlyTransfers: Joi.string().optional(),
      billingMethods: Joi.array().items(Joi.string().optional()).optional(),
      processedCreditCardsBefore: Joi.string().optional(),
      fraudAndChargebacksRates: Joi.array().items(Joi.array().items(Joi.string().optional())).optional(),
      processingAndChargebacksHistory: Joi.array().items(Joi.any()).optional(),
      isRevenueFromBusinessActivity: Joi.string().optional(),
      freeTrialZeroAmount: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional()
    }).optional(),
    contacts: Joi.object()
      .pattern(
        Joi.string(),
        Joi.object({
          name: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
          email: Joi.alternatives().try(Joi.string().email(), Joi.allow(null)).optional(),
          phone: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
          other: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional()
        })
      )
      .optional(),
    cryptoIndustry: Joi.object({
      cryptoActivityType: Joi.array().items(Joi.string()).optional(),
      cryptocurrenciesList: Joi.array().items(Joi.string()).optional(),
      nativeTokensSold: Joi.boolean().optional(),
      nativeTokensTypes: Joi.alternatives().try(Joi.array().items(Joi.string()), Joi.allow(null)).optional(),
      customerBase: Joi.array().items(Joi.string()).optional(),
      conversions: Joi.array().items(Joi.string()).optional(),
      KYCStages: Joi.array().items(Joi.string()).optional(),
      tools: Joi.alternatives().try(Joi.boolean(), Joi.allow(null)).optional(),
      withdrawals: Joi.boolean().optional(),
      withdrawalsWalletTypes: Joi.alternatives().try(Joi.array().items(Joi.string()), Joi.allow(null)).optional(),
      withdrawalsProcess: Joi.string().optional(),
      risks: Joi.boolean().optional(),
      yearsEngaging: Joi.string().optional(),
      training: Joi.boolean().optional(),
      pastTransactions: Joi.string().optional(),
      professionalBackground: Joi.string().optional()
    }).optional(),
    MSBIndustry: Joi.object({
      employeesNumber: Joi.string().optional(),
      annualTurnover: Joi.string().optional(),
      serviceTypes: Joi.array().items(Joi.string()).optional(),
      customerBase: Joi.array().items(Joi.string()).optional(),
      internationalTransactionsRatio: Joi.string().optional(),
      enhancedDueDiligence: Joi.alternatives().try(Joi.boolean(), Joi.allow(null)).optional(),
      KYCStages: Joi.array().items(Joi.string()).optional(),
      customerIDVerification: Joi.array().items(Joi.string()).optional(),
      preventionSystem: Joi.alternatives().try(Joi.boolean(), Joi.allow(null)).optional(),
      complianceEmployeesNumber: Joi.string().optional(),
      highRiskClientsRatio: Joi.string().optional(),
      complianceAudits: Joi.string().optional(),
      cryptoInvolvement: Joi.alternatives().try(Joi.boolean(), Joi.allow(null)).optional(),
      cryptoExposureDetails: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional()
    }).optional(),
    adultServices: Joi.object({
      contentSources: Joi.array().items(Joi.string()).optional(),
      contentConsentAgreements: Joi.alternatives().try(Joi.boolean(), Joi.allow(null)).optional(),
      contentVerificationMethods: Joi.string().optional(),
      contentPoviderAgeAndIDVerification: Joi.string().optional(),
      contentReviewProcess: Joi.string().optional(),
      liveStreamingControl: Joi.boolean().optional(),
      liveStreamingMonitoringAndRemoval: Joi.string().optional(),
      removalAppealProcess: Joi.string().optional(),
      marketingAndSearchTerms: Joi.string().optional(),
      potentiallyIllegalContentPreventionProcess: Joi.string().optional(),
      potentiallyIllegalContentComplaintProcess: Joi.boolean().optional(),
      illegalContentReporting: Joi.boolean().optional(),
      traffickingAndAbusePolicy: Joi.string().optional(),
      consentAgreementsRetention: Joi.boolean().optional(),
      offerAIContent: Joi.alternatives().try(Joi.boolean(), Joi.allow(null)).optional(),
      AIContentSources: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
      AIContentAgreements: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
      confirmNoAIDeepfakes: Joi.alternatives().try(Joi.boolean(), Joi.allow(null)).optional()
    }).optional(),
    datingEscortData: Joi.object({
      antiTraffickingProgram: Joi.string().optional(),
      contentReviewProcess: Joi.string().optional(),
      contentRemovalAppeals: Joi.string().optional(),
      marketingAndSearchTermsCompliance: Joi.string().optional(),
      contentProviderConsentsDepiction: Joi.string().optional(),
      consentPublicDistribution: Joi.string().optional(),
      consentContentDownload: Joi.string().optional(),
      consentsValidationAndStorage: Joi.string().optional(),
      evidenceValidationAndRetention: Joi.string().optional(),
      marketingStrategy: Joi.string().optional(),
      fakeBotPrevention: Joi.string().optional()
    }).optional(),
    industrySpecificStatement: Joi.object({
      highValueKYC: Joi.boolean().optional(),
      insurance: Joi.boolean().optional(),
      highValuePlayerKYC: Joi.boolean().optional(),
      payoutReserves: Joi.boolean().optional(),
      bonusAbusePrevention: Joi.boolean().optional(),
      selfExclusionOrResponsibleTools: Joi.boolean().optional(),
      coolingOff: Joi.boolean().optional(),
      highValueGoods: Joi.boolean().optional(),
      gamblingInclusion: Joi.boolean().optional(),
      childProtection: Joi.boolean().optional(),
      ageVerification: Joi.boolean().optional(),
      guestOrAnnonymousAccounts: Joi.boolean().optional(),
      spendingLimits: Joi.boolean().optional(),
      tieredAccountSystem: Joi.boolean().optional(),
      kycStages: Joi.string().optional(),
      forexLicenseHolder: Joi.string().optional(),
      gamblingLicense: Joi.string().optional(),
      geofencing: Joi.string().optional(),
      fraudPrevention: Joi.string().optional(),
      minimumAgeRequirements: Joi.string().optional(),
      payoutWithdrawals: Joi.string().optional(),
      cardRevenueRatio: Joi.string().optional(),
      suspiciousPatternsPrevention: Joi.string().optional(),
      massBettingPrevention: Joi.string().optional(),
      singleOrMultiVendor: Joi.string().optional(),
      transacitonProcessing: Joi.string().optional(),
      disputeResolution: Joi.string().optional(),
      vendorsNumber: Joi.string().optional(),
      suspiciousAccountHandling: Joi.string().optional(),
      payoutReserveManagement: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
      selfExclusionOrResponsibleToolsDescription: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
      consumerProtectionMethods: Joi.array().items(Joi.string()).optional(),
      marketplaceTypes: Joi.array().items(Joi.string()).optional(),
      vendorOnboarding: Joi.array().items(Joi.string()).optional(),
      gamingServiceTypes: Joi.array().items(Joi.string()).optional(),
      currencyTypes: Joi.array().items(Joi.string()).optional(),
      transactionTypes: Joi.array().items(Joi.string()).optional(),
      accountCreationInformation: Joi.array().items(Joi.string()).optional(),
      fraudPreventionGmg: Joi.array().items(Joi.string()).optional(),
      digWalTypes: Joi.array().items(Joi.string()).optional(),
      serviceTypesDigWal: Joi.array().items(Joi.string()).optional(),
      targetAudience: Joi.array().items(Joi.string()).optional(),
      onboardingInformation: Joi.array().items(Joi.string()).optional(),
      IDVerification: Joi.array().items(Joi.string()).optional()
    }).optional(),
    licenses: Joi.object()
      .pattern(
        Joi.string(),
        Joi.object({
          number: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
          website: Joi.alternatives().try(Joi.string().email(), Joi.allow(null)).optional(),
          issuingBody: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional()
        })
      )
      .optional()
  }).optional(),
  correspondenceEmail: Joi.string().optional()
});

// Extend the base schema properly
export const schemaAcquiringEntityValidation = baseAcquiringEntitySchema.append<AcquiringEntity>({
  principalRawData: Joi.array().items(principalSchema).optional()
});

export const manualApproveAcquireSchema = Joi.object<ManualApproveAcquire>({
  typeOfAction: Joi.string().required().lowercase().valid('approve', 'reject'),
  acquiringEntityId: Joi.string().required(),
  matchDataId: Joi.string().required()
});

export const manualApproveAcquireByRefNumberSchema = Joi.object<ManualApproveAcquireByRefNumber>({
  typeOfAction: Joi.string().required().lowercase().valid('approve', 'reject'),
  acquiringEntityId: Joi.string().required(),
  mastercardRefNumberMatched: Joi.string().required()
});

const principalUpdateSchema = Joi.object<Principal & { _id: string }>({
  _id: Joi.string().optional(),
  firstName: Joi.alternatives().try(Joi.string(), Joi.allow(null)).required(),
  middleName: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  lastName: Joi.when('firstName', {
    is: Joi.exist().not(null),
    then: Joi.string().required(),
    otherwise: Joi.string().allow(null).optional()
  }),
  email: Joi.alternatives().try(Joi.string().email(), Joi.allow(null)).optional(),
  phone: Joi.when('firstName', {
    is: Joi.exist().not(null),
    then: Joi.string().required(),
    otherwise: Joi.string().allow(null).optional()
  }),
  country: Joi.when('firstName', {
    is: Joi.exist().not(null),
    then: Joi.string().required(),
    otherwise: Joi.string().allow(null).optional()
  }),
  state: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  city: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  address1: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  address2: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  zip: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  passportNumber: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  driverLicenseNumber: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  dateOfBirth: Joi.alternatives().try(Joi.string().isoDate(), Joi.allow(null)).optional(),
  positionInCompany: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional()
});

export const baseAcquiringEntityUpdateSchema = Joi.object<AcquiringEntity & { _id: string }>({
  _id: Joi.string().required(),
  name: Joi.string().required(),
  registrationNumber: Joi.string().required(),
  incorporationDate: Joi.string().optional(),
  email: Joi.alternatives().try(Joi.string().email(), Joi.allow(null)).optional(),
  country: Joi.string().required(),
  state: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  city: Joi.string().required(),
  zip: Joi.string().required(),
  address1: Joi.string().required(),
  address2: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  tradeOverInternet: Joi.boolean().optional(),
  websites: Joi.array()
    .items(
      Joi.object({
        url: Joi.string().uri().required(),
        statementDescriptor: Joi.string().optional(),
        cityField: Joi.string().optional()
      })
    )
    .optional(),
  businessCategoryVmss: Joi.alternatives().try(
    Joi.string().valid(...Object.values(BusinessCategoryVmss)),
    Joi.allow(null)
  ),
  taxId: Joi.alternatives().try(Joi.string(), Joi.allow(null)).optional(),
  principalsIds: Joi.array().items(principalUpdateSchema).optional(),
  // principalsIds: Joi.array().items(principalSchema).optional(),

  phone: Joi.array().items(Joi.string().required()).optional(),
  tradingName: Joi.string().optional(),
  address1TradingAs: Joi.string().optional(),
  address2TradingAs: Joi.string().optional(),
  cityTradingAs: Joi.string().optional(),
  stateTradingAs: Joi.string().optional(),
  zipTradingAs: Joi.string().optional(),
  countryTradingAs: Joi.string().optional()
});
