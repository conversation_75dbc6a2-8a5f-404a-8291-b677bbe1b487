import 'module-alias/register';
import { Express } from 'express';
import dotenv from 'dotenv';
import http from 'http';

dotenv.config();

import mongoose from './config/mongoose';
import logger from './utils/logger';
import { app } from './app';
import { startKafkaConsumers } from './kafka/consumers';
import { connectToKafka, disconnectFromKafka } from './services/kafkaService';
import { startAcquiringEntityWebsiteRetryCronJobs } from './services/cronService';
import { IS_PRODUCTION } from './config/config';
let server: http.Server | undefined;

const PORT = process.env.PORT || 3000;

mongoose.connectDB(() => afterConnect(app), false);

app.on('ready', async () => {
  try {
    await connectToKafka();

    if (IS_PRODUCTION) {
      await startKafkaConsumers();
    } else {
      // This is because when we need to connect to the consumers it takes a lot of
      // time and we don't want to wait in development
      startKafkaConsumers();
    }
    server = app.listen(PORT, () => {
      logger.info(`Server is listening on port: ${PORT}`);
      if (process.env.START_CRON_JOB?.toLowerCase() === 'true') {
        startAcquiringEntityWebsiteRetryCronJobs();
      }
    });
  } catch (error: any) {
    logger.error(`❌ Error starting server: ${error.message}`);
  }
});

process.on('SIGTERM', () => {
  shutdownServer();
});

// To disconnect the server when we press 'ctrl + c' on the terminal
process.on('SIGINT', () => {
  shutdownServer();
});

/**
 * Function to be executed after successful connection
 *
 * @param app The Express app
 */
async function afterConnect(app: Express): Promise<void> {
  app.emit('ready');
}

async function shutdownServer() {
  try {
    if (server) {
      server.close(() => logger.info('🛑 Server stopped.'));
    }

    await disconnectFromKafka();
    new Promise((resolve) => setTimeout(resolve, 1000));
    process.exit(0);
  } catch (error: any) {
    logger.error(`❌ Error while shutting down: ${error.message}`);
    process.exit(1);
  }
}
