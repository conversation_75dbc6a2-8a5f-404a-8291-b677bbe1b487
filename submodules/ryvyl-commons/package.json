{"name": "ryvyl-commons", "version": "1.0.0", "main": "index.js", "scripts": {"format": "pretty-quick --staged", "lint:check": "eslint . --ext ts --ext tsx --ext js --ext jsx", "lint:fix": "eslint . --ext ts --ext tsx --ext js --ext jsx --fix", "test": "jest --verbose --setupFiles dotenv/config"}, "repository": {"type": "git", "url": "git+https://github.com/encorp-io/ryvyl-commons"}, "keywords": ["submodule", "common", "auth"], "author": "Encorp.io", "license": "ISC", "bugs": {"url": "https://github.com/encorp-io/ryvyl-commons/issues"}, "homepage": "https://github.com/encorp-io/ryvyl-commons#readme", "description": "", "dependencies": {"dotenv": "^16.4.7", "express": "^4.21.2", "express-validator": "^7.2.1", "i18n-iso-countries": "^7.14.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "mongoose": "^8.10.1", "node-cron": "^3.0.3", "openai": "^4.89.0", "winston": "^3.17.0", "winston-loggly-bulk": "^3.3.2"}, "devDependencies": {"@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.8", "@types/node-cron": "^3.0.11", "@types/winston-loggly-bulk": "^3.0.6", "@typescript-eslint/eslint-plugin": "5.3.1", "@typescript-eslint/parser": "5.3.1", "eslint": "8.2.0", "eslint-config-google": "0.14.0", "eslint-plugin-jsdoc": "37.0.3", "husky": "^9.1.6", "pretty-quick": "^4.1.1", "ts-jest": "^29.2.6"}}