name: Create and publish a container with 'latest' tag

on:
  push:
    branches:
      - 'dev'
  workflow_dispatch:
    inputs:
      image_tag:
        description: 'tag for this image build'
        required: true
        default: 'development'

jobs:
  build-development:
    runs-on: ubuntu-latest
    env:
      PILLAR: development
    steps:
      - name: Checkout code repository with submodules
        uses: actions/checkout@v2
        with:
          submodules: true # Fetch submodules
          fetch-depth: 0 # Ensure full commit history
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }} # Set the access token
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2 # Use the updated version
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USERNAME }} # Replace with your secret
          password: ${{ secrets.DOCKER_SECRET }} # Replace with your secret

      - name: Build and push the image
        uses: docker/build-push-action@v2
        with:
          context: '.'
          push: true
          tags: ghcr.io/encorp-io/ryvyl-merchant-profile:development
          build-args: PROFILE=${{ env.PILLAR }}
