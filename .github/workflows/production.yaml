name: Create and publish a container
on:
  push:
    branches:
      - 'prod'
  workflow_dispatch:
    inputs:
      image_tag:
        description: 'tag for this image build'
        required: true
        default: 'prod'

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      PILLAR: production
    steps:
      - name: Checkout code repository with submodules
        uses: actions/checkout@v2
        with:
          submodules: true # Fetch submodules
          fetch-depth: 0 # Ensure full commit history
          token: ${{ secrets.PERSONAL_ACCESS_TOKEN }} # Set the access token
      - name: Look for tag
        id: istagged
        run: echo "PILLAR=production" >> $GITHUB_ENV
      - name: Docker meta
        id: meta
        if: env.PILLAR == 'production'
        uses: docker/metadata-action@v3
        with:
          images: martinkey/interface

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ secrets.GHCR_USERNAME }} # Replace with your secret
          password: ${{ secrets.DOCKER_SECRET }} # Replace with your secret
      - name: Build and push with Tag (workflow triggered manually)
        uses: docker/build-push-action@v2
        if: env.PILLAR == 'production' && github.event_name == 'workflow_dispatch'
        with:
          context: '.'
          push: true
          tags: ghcr.io/encorp-io/ryvyl-merchant-profile:${{ github.event.inputs.image_tag }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: PROFILE=${{ env.PILLAR }}
      - name: Build and push with Tag (workflow triggered by tag)
        uses: docker/build-push-action@v2
        if: env.PILLAR == 'production' && github.event_name == 'push'
        with:
          context: '.'
          push: true
          tags: ghcr.io/encorp-io/ryvyl-merchant-profile:prod
          labels: ${{ steps.meta.outputs.labels }}
          build-args: PROFILE=${{ env.PILLAR }}
