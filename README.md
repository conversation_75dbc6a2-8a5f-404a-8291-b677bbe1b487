# Ryvyl Merchant Profile API

This API handles merchant profile, acquiring entity management, and compliance checks against Mastercard Match Pro and Visa VMSS systems.

## Getting Started

1. Clone this repository
2. Run `npm install` to install dependencies
3. Configure your environment variables in `.env` file (see `.env.example` for required variables)
4. Run `npm start` to start the development server

## API Features

- Acquiring entity management
- Principal management
- Mastercard Match Pro integration
- Visa VMSS integration
- Compliance screening and reporting
