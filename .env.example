ENV=<DEV or PROD>
PORT=<Port Number>

# Mongoose
MONGODB_HOST=<MongoDB Host>
MONGODB_PORT=<MongoDB Port>
MONGODB_DATABASE_NAME=<MongoDB Database Name>
MONGODB_USERNAME=<MongoDB Username>
MONGODB_PASSWORD=<MongoDB Password>
MONGODB_URI=<MongoDB URI>

# Logger
LOGGER_UPLOAD_LOGS=<true or false>
LOGGER_SUBDOMAIN=<Subdomain for the logger>
LOGGER_TOKEN=<Token for the logger>
LOGGER_TAGS=<Tags for the logger separated by commas>

# External Services
RYVYL_VISA_VMSS_SERVICE_BASE_URL=<Base URL of Ryvyl Visa VMSS Service>
RYVYL_MASTERCARD_MATCH_PRO_SERVICE_BASE_URL=<Base URL of Ryvyl Mastercard Match Pro Service>
MCC_SERVICE_CLASSIFIER_BASE_URL=<Base URL of Mcc classifier Service>

JWT_ALGORITHM=<JWT algoritam>
JWT_PUBLIC_KEY=<Public key>

KAFKA_BROKER_URL=<Kafka URL server>
KAFKA_GROUP_ID=<Group id>

SUMSUB_RYVYL_SERVICE_BASE_URL=<Base URL of Ryvyl Sumsub service>
SUMSUB_ACQUIRING_ENTITY_LEVEL_NAME=<Acquiring company level name from sumsub>
SUMSUB_ACQUIRING_ENTITY_URL_LEVEL_NAME=<Acquiring url level name from sumsub>
SUMSUB_ACQUIRING_ENTITY_BENEFICIARY_LEVEL_NAME=<Acquiring individual level name from sumsub>
