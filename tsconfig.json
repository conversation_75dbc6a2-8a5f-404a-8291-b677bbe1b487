// Taken from https://github.com/tsconfig/bases/blob/main/bases/node16.json (Centralized Recommendations for TSConfig bases)
{
  "compilerOptions": {
    "module": "commonjs",
    "target": "es6",

    "allowJs": true,
    "outDir": "./dist",
    "rootDir": "./",

    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,

    "baseUrl": ".",
    "paths": {
      "@submodules/*": ["submodules/*"]
    }
  },

  "include": ["src/**/*", "submodules/ryvyl-commons/types/**/*"],
  "exclude": ["node_modules", "**/*.spec.ts"]
}
